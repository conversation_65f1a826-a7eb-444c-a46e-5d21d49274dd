from django import forms


class ContactForm(forms.Form):
    SERVICE_CHOICES = [
        ('', 'Select a Service'),
        ('graphic_design', 'Graphic Design'),
        ('web_development', 'Web Development'),
        ('finance_consulting', 'Finance Consulting'),
        ('multiple', 'Multiple Services'),
        ('other', 'Other'),
    ]

    BUDGET_CHOICES = [
        ('', 'Select Budget Range'),
        ('under_500', 'Under $500'),
        ('500_1000', '$500 - $1,000'),
        ('1000_2500', '$1,000 - $2,500'),
        ('2500_5000', '$2,500 - $5,000'),
        ('over_5000', 'Over $5,000'),
        ('discuss', 'Let\'s Discuss'),
    ]

    TIMELINE_CHOICES = [
        ('', 'Select Timeline'),
        ('asap', 'ASAP'),
        ('1_week', 'Within 1 Week'),
        ('2_weeks', 'Within 2 Weeks'),
        ('1_month', 'Within 1 Month'),
        ('flexible', 'Flexible'),
    ]

    name = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Your Full Name',
            'required': True,
        })
    )

    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Your Email Address',
            'required': True,
        })
    )

    phone = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Phone Number (Optional)',
        })
    )

    company = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Company/Organization (Optional)',
        })
    )

    service = forms.ChoiceField(
        choices=SERVICE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-control',
            'required': True,
        })
    )

    budget = forms.ChoiceField(
        choices=BUDGET_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control',
        })
    )

    timeline = forms.ChoiceField(
        choices=TIMELINE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control',
        })
    )

    subject = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Project Title/Subject',
            'required': True,
        })
    )

    message = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'placeholder': 'Tell me about your project, goals, and any specific requirements...',
            'rows': 6,
            'required': True,
        })
    )

    newsletter = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input',
        }),
        label='Subscribe to newsletter for updates and tips'
    )

    # Honeypot field (hidden from users, should remain empty)
    website = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'style': 'display: none !important;',
            'tabindex': '-1',
            'autocomplete': 'off',
        })
    )
    
    def clean_name(self):
        name = self.cleaned_data.get('name')
        if not name:
            raise forms.ValidationError('Name is required.')

        # Remove extra whitespace
        name = ' '.join(name.split())

        if len(name) < 2:
            raise forms.ValidationError('Name must be at least 2 characters long.')

        if len(name) > 100:
            raise forms.ValidationError('Name must be less than 100 characters.')

        # Check for suspicious patterns
        import re
        if re.search(r'[<>"\']', name):
            raise forms.ValidationError('Name contains invalid characters.')

        # Check for spam patterns
        spam_patterns = ['http', 'www.', '.com', 'viagra', 'casino', 'loan']
        if any(pattern in name.lower() for pattern in spam_patterns):
            raise forms.ValidationError('Invalid name format.')

        return name

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if not email:
            raise forms.ValidationError('Email is required.')

        # Additional email validation
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            raise forms.ValidationError('Please enter a valid email address.')

        # Check for disposable email domains
        disposable_domains = [
            '10minutemail.com', 'tempmail.org', 'guerrillamail.com',
            'mailinator.com', 'throwaway.email'
        ]
        domain = email.split('@')[1].lower()
        if domain in disposable_domains:
            raise forms.ValidationError('Please use a permanent email address.')

        return email.lower()

    def clean_phone(self):
        phone = self.cleaned_data.get('phone')
        if phone:
            # Remove all non-digit characters
            import re
            phone = re.sub(r'\D', '', phone)

            # Validate phone number length
            if len(phone) < 10 or len(phone) > 15:
                raise forms.ValidationError('Please enter a valid phone number.')

        return phone

    def clean_subject(self):
        subject = self.cleaned_data.get('subject')
        if not subject:
            raise forms.ValidationError('Subject is required.')

        subject = ' '.join(subject.split())

        if len(subject) < 5:
            raise forms.ValidationError('Subject must be at least 5 characters long.')

        # Check for spam patterns
        spam_patterns = ['free money', 'click here', 'urgent', 'congratulations', 'winner']
        if any(pattern in subject.lower() for pattern in spam_patterns):
            raise forms.ValidationError('Subject appears to be spam.')

        return subject

    def clean_message(self):
        message = self.cleaned_data.get('message')
        if not message:
            raise forms.ValidationError('Message is required.')

        message = message.strip()

        if len(message) < 10:
            raise forms.ValidationError('Message must be at least 10 characters long.')

        if len(message) > 2000:
            raise forms.ValidationError('Message must be less than 2000 characters.')

        # Check for spam patterns
        spam_indicators = ['http://', 'https://', 'www.', 'click here', 'free money', 'viagra']
        spam_count = sum(1 for indicator in spam_indicators if indicator in message.lower())

        if spam_count >= 2:
            raise forms.ValidationError('Message appears to be spam.')

        # Check for excessive repetition
        words = message.lower().split()
        if len(words) > 10:
            unique_words = set(words)
            if len(unique_words) / len(words) < 0.3:  # Less than 30% unique words
                raise forms.ValidationError('Message appears to be spam or repetitive.')

        return message

    def clean(self):
        cleaned_data = super().clean()

        # Cross-field validation
        name = cleaned_data.get('name', '')
        email = cleaned_data.get('email', '')
        message = cleaned_data.get('message', '')

        # Check if name appears in email (potential spam)
        if name and email:
            name_parts = name.lower().split()
            email_local = email.split('@')[0].lower()
            if all(part in email_local for part in name_parts if len(part) > 2):
                # This is actually normal, so we'll allow it
                pass

        # Honeypot field check (if implemented)
        honeypot = cleaned_data.get('website')  # Hidden field
        if honeypot:
            raise forms.ValidationError('Spam detected.')

        return cleaned_data
