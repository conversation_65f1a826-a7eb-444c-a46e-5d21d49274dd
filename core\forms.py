from django import forms


class ContactForm(forms.Form):
    SERVICE_CHOICES = [
        ('', 'Select a Service'),
        ('graphic_design', 'Graphic Design'),
        ('web_development', 'Web Development'),
        ('finance_consulting', 'Finance Consulting'),
        ('multiple', 'Multiple Services'),
        ('other', 'Other'),
    ]

    BUDGET_CHOICES = [
        ('', 'Select Budget Range'),
        ('under_500', 'Under $500'),
        ('500_1000', '$500 - $1,000'),
        ('1000_2500', '$1,000 - $2,500'),
        ('2500_5000', '$2,500 - $5,000'),
        ('over_5000', 'Over $5,000'),
        ('discuss', 'Let\'s Discuss'),
    ]

    TIMELINE_CHOICES = [
        ('', 'Select Timeline'),
        ('asap', 'ASAP'),
        ('1_week', 'Within 1 Week'),
        ('2_weeks', 'Within 2 Weeks'),
        ('1_month', 'Within 1 Month'),
        ('flexible', 'Flexible'),
    ]

    name = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Your Full Name',
            'required': True,
        })
    )

    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Your Email Address',
            'required': True,
        })
    )

    phone = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Phone Number (Optional)',
        })
    )

    company = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Company/Organization (Optional)',
        })
    )

    service = forms.ChoiceField(
        choices=SERVICE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-control',
            'required': True,
        })
    )

    budget = forms.ChoiceField(
        choices=BUDGET_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control',
        })
    )

    timeline = forms.ChoiceField(
        choices=TIMELINE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control',
        })
    )

    subject = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Project Title/Subject',
            'required': True,
        })
    )

    message = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'placeholder': 'Tell me about your project, goals, and any specific requirements...',
            'rows': 6,
            'required': True,
        })
    )

    newsletter = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input',
        }),
        label='Subscribe to newsletter for updates and tips'
    )
    
    def clean_name(self):
        name = self.cleaned_data.get('name')
        if len(name) < 2:
            raise forms.ValidationError('Name must be at least 2 characters long.')
        return name
    
    def clean_message(self):
        message = self.cleaned_data.get('message')
        if len(message) < 10:
            raise forms.ValidationError('Message must be at least 10 characters long.')
        return message
