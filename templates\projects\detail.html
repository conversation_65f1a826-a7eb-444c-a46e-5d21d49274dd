{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}
{% block description %}{{ meta_description }}{% endblock %}

{% block content %}
<!-- Project Hero Section -->
<section class="section-padding" style="background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-emerald) 100%); color: white; margin-top: 80px;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="fade-in">
                    <nav aria-label="breadcrumb" class="mb-3">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'core:home' %}" style="color: var(--light-gold);">Home</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'projects:portfolio' %}" style="color: var(--light-gold);">Projects</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'projects:portfolio_by_category' project.category.slug %}" style="color: var(--light-gold);">{{ project.category.name }}</a></li>
                            <li class="breadcrumb-item active" aria-current="page" style="color: white;">{{ project.title }}</li>
                        </ol>
                    </nav>
                    <h1 class="vintage-heading" style="color: white; font-size: 3rem;">{{ project.title }}</h1>
                    <p class="lead" style="color: var(--light-gold);">{{ project.short_description }}</p>
                    <div class="project-meta mt-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="meta-item">
                                    <i class="fas fa-folder me-2"></i>
                                    <span>{{ project.category.name }}</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="meta-item">
                                    <i class="fas fa-calendar me-2"></i>
                                    <span>{{ project.project_date|date:"M Y" }}</span>
                                </div>
                            </div>
                            {% if project.client %}
                            <div class="col-md-3">
                                <div class="meta-item">
                                    <i class="fas fa-user me-2"></i>
                                    <span>{{ project.client }}</span>
                                </div>
                            </div>
                            {% endif %}
                            {% if project.project_url %}
                            <div class="col-md-3">
                                <div class="meta-item">
                                    <a href="{{ project.project_url }}" target="_blank" class="btn btn-outline-light btn-sm">
                                        <i class="fas fa-external-link-alt me-2"></i>View Live
                                    </a>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 text-center">
                <div class="fade-in">
                    {% if project.featured_image %}
                    <img src="{{ project.featured_image.url }}" alt="{{ project.title }}" class="img-fluid rounded shadow-lg" style="max-height: 400px; object-fit: cover;">
                    {% else %}
                    <div class="project-placeholder">
                        <i class="{{ project.category.icon }}" style="font-size: 5rem; color: var(--gold);"></i>
                        <p class="mt-3">{{ project.category.name }} Project</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Project Content -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <!-- Project Description -->
                <div class="fade-in mb-5">
                    <h2 class="vintage-heading">Project Overview</h2>
                    <div class="project-description">
                        {{ project.description|linebreaks }}
                    </div>
                </div>

                <!-- Technologies & Tools -->
                {% if technologies or tools %}
                <div class="fade-in mb-5">
                    <h3 class="vintage-heading">Technologies & Tools</h3>
                    <div class="row">
                        {% if technologies %}
                        <div class="col-md-6">
                            <h5>Technologies Used</h5>
                            <div class="tech-tags">
                                {% for tech in technologies %}
                                <span class="tech-tag">{{ tech }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                        {% if tools %}
                        <div class="col-md-6">
                            <h5>Tools & Software</h5>
                            <div class="tech-tags">
                                {% for tool in tools %}
                                <span class="tech-tag tool-tag">{{ tool }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- Project Gallery -->
                {% if project.images.all or gallery_images %}
                <div class="fade-in mb-5">
                    <h3 class="vintage-heading">Project Gallery</h3>
                    <div class="row g-3">
                        {% for image in project.images.all %}
                        <div class="col-md-6">
                            <div class="gallery-item">
                                <img src="{{ image.image.url }}" alt="{{ image.caption }}" class="img-fluid rounded shadow" data-bs-toggle="modal" data-bs-target="#galleryModal" data-bs-slide-to="{{ forloop.counter0 }}">
                                {% if image.caption %}
                                <p class="gallery-caption mt-2">{{ image.caption }}</p>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Testimonials -->
                {% if project.testimonials.all %}
                <div class="fade-in mb-5">
                    <h3 class="vintage-heading">Client Feedback</h3>
                    {% for testimonial in project.testimonials.all %}
                    <div class="testimonial-card">
                        <div class="testimonial-content">
                            <p>"{{ testimonial.content }}"</p>
                        </div>
                        <div class="testimonial-author">
                            <strong>{{ testimonial.name }}</strong>
                            {% if testimonial.position and testimonial.company %}
                            <span class="text-muted">{{ testimonial.position }} at {{ testimonial.company }}</span>
                            {% elif testimonial.company %}
                            <span class="text-muted">{{ testimonial.company }}</span>
                            {% endif %}
                            <div class="rating">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= testimonial.rating %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% else %}
                                        <i class="far fa-star text-muted"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="project-sidebar">
                    <!-- Project Actions -->
                    <div class="sidebar-card fade-in">
                        <h4>Project Actions</h4>
                        <div class="d-grid gap-2">
                            {% if project.project_url %}
                            <a href="{{ project.project_url }}" target="_blank" class="btn btn-vintage">
                                <i class="fas fa-external-link-alt me-2"></i>View Live Project
                            </a>
                            {% endif %}
                            {% if project.github_url %}
                            <a href="{{ project.github_url }}" target="_blank" class="btn btn-outline-dark">
                                <i class="fab fa-github me-2"></i>View Code
                            </a>
                            {% endif %}
                            <a href="{% url 'core:contact' %}" class="btn btn-outline-primary">
                                <i class="fas fa-envelope me-2"></i>Discuss Similar Project
                            </a>
                        </div>
                    </div>

                    <!-- Related Projects -->
                    {% if related_projects %}
                    <div class="sidebar-card fade-in">
                        <h4>Related Projects</h4>
                        {% for related in related_projects %}
                        <div class="related-project">
                            <div class="row align-items-center">
                                <div class="col-4">
                                    {% if related.featured_image %}
                                    <img src="{{ related.featured_image.url }}" alt="{{ related.title }}" class="img-fluid rounded">
                                    {% else %}
                                    <div class="related-placeholder">
                                        <i class="{{ related.category.icon }}"></i>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="col-8">
                                    <h6><a href="{{ related.get_absolute_url }}">{{ related.title }}</a></h6>
                                    <small class="text-muted">{{ related.category.name }}</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- Contact CTA -->
                    <div class="sidebar-card fade-in" style="background: var(--gradient-primary); color: white;">
                        <h4 style="color: white;">Start Your Project</h4>
                        <p>Ready to create something amazing? Let's discuss your project and bring your vision to life.</p>
                        <a href="{% url 'core:contact' %}" class="btn btn-light">
                            <i class="fas fa-rocket me-2"></i>Get Started
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Navigation -->
<section class="py-4" style="background-color: var(--light-bg);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <a href="{% url 'projects:portfolio' %}" class="btn btn-outline-dark">
                    <i class="fas fa-arrow-left me-2"></i>Back to Portfolio
                </a>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="{% url 'core:contact' %}" class="btn btn-vintage">
                    <i class="fas fa-envelope me-2"></i>Start Your Project
                </a>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
    .project-meta .meta-item {
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 0.5rem;
    }
    
    .project-placeholder {
        background: rgba(255, 255, 255, 0.1);
        padding: 3rem;
        border-radius: var(--radius-medium);
        text-align: center;
    }
    
    .project-description {
        font-size: 1.1rem;
        line-height: 1.8;
        color: var(--text-dark);
    }
    
    .tech-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .tech-tag {
        background: var(--gradient-primary);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: var(--radius-round);
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .tool-tag {
        background: linear-gradient(45deg, var(--gold), var(--dark-gold));
    }
    
    .gallery-item img {
        cursor: pointer;
        transition: transform 0.3s ease;
    }
    
    .gallery-item img:hover {
        transform: scale(1.05);
    }
    
    .gallery-caption {
        font-size: 0.9rem;
        color: var(--text-muted);
        text-align: center;
    }
    
    .testimonial-card {
        background: white;
        padding: 2rem;
        border-radius: var(--radius-medium);
        box-shadow: 0 10px 30px var(--shadow-light);
        margin-bottom: 2rem;
        border-left: 4px solid var(--emerald-green);
    }
    
    .testimonial-content p {
        font-style: italic;
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }
    
    .testimonial-author {
        border-top: 1px solid #eee;
        padding-top: 1rem;
    }
    
    .sidebar-card {
        background: white;
        padding: 2rem;
        border-radius: var(--radius-medium);
        box-shadow: 0 10px 30px var(--shadow-light);
        margin-bottom: 2rem;
        border: 1px solid var(--shadow-light);
    }
    
    .sidebar-card h4 {
        color: var(--emerald-green);
        margin-bottom: 1.5rem;
    }
    
    .related-project {
        padding: 1rem 0;
        border-bottom: 1px solid #eee;
    }
    
    .related-project:last-child {
        border-bottom: none;
    }
    
    .related-project h6 a {
        color: var(--text-dark);
        text-decoration: none;
    }
    
    .related-project h6 a:hover {
        color: var(--emerald-green);
    }
    
    .related-placeholder {
        background: var(--light-bg);
        padding: 1rem;
        border-radius: var(--radius-small);
        text-align: center;
        color: var(--emerald-green);
        font-size: 1.5rem;
    }
    
    .breadcrumb {
        background: none;
        padding: 0;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: var(--light-gold);
    }
    
    @media (max-width: 768px) {
        .project-meta .row > div {
            margin-bottom: 1rem;
        }
        
        .sidebar-card {
            margin-top: 2rem;
        }
        
        .tech-tags {
            justify-content: center;
        }
    }
</style>
{% endblock %}
