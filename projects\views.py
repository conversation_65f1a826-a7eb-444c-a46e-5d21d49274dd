from django.shortcuts import render, get_object_or_404
from django.core.paginator import Paginator
from .models import Project, ProjectCategory, Testimonial


def portfolio(request):
    """Portfolio page view with project showcase and filtering"""
    # Get all categories for filtering
    categories = ProjectCategory.objects.filter(is_active=True)

    # Get published projects
    projects = Project.objects.filter(status='published').select_related('category')

    # Filter by category if specified
    category_filter = request.GET.get('category')
    if category_filter:
        projects = projects.filter(category__slug=category_filter)

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        projects = projects.filter(
            title__icontains=search_query
        ) | projects.filter(
            description__icontains=search_query
        ) | projects.filter(
            technologies__icontains=search_query
        )

    # Pagination
    paginator = Paginator(projects, 9)  # 9 projects per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Featured testimonials
    testimonials = Testimonial.objects.filter(is_featured=True)[:3]

    context = {
        'page_title': 'Projects - <PERSON>',
        'meta_description': 'Explore <PERSON>\'s portfolio of graphic design, web development, and finance projects. Professional work samples and case studies.',
        'categories': categories,
        'projects': page_obj,
        'testimonials': testimonials,
        'current_category': category_filter,
        'search_query': search_query,
        'total_projects': projects.count(),
    }
    return render(request, 'projects/portfolio.html', context)


def portfolio_by_category(request, category_slug):
    """Portfolio filtered by specific category"""
    category = get_object_or_404(ProjectCategory, slug=category_slug, is_active=True)

    projects = Project.objects.filter(
        category=category,
        status='published'
    ).select_related('category')

    # Pagination
    paginator = Paginator(projects, 9)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_title': f'{category.name} Projects - Muhammad Trinanda',
        'meta_description': f'Explore Muhammad Trinanda\'s {category.name.lower()} projects and portfolio.',
        'category': category,
        'projects': page_obj,
        'total_projects': projects.count(),
    }
    return render(request, 'projects/category.html', context)


def project_detail(request, slug):
    """Individual project detail page"""
    project = get_object_or_404(
        Project.objects.select_related('category').prefetch_related('images', 'testimonials'),
        slug=slug,
        status='published'
    )

    # Related projects from same category
    related_projects = Project.objects.filter(
        category=project.category,
        status='published'
    ).exclude(id=project.id)[:3]

    context = {
        'page_title': f'{project.title} - Muhammad Trinanda',
        'meta_description': project.meta_description or project.short_description,
        'project': project,
        'related_projects': related_projects,
        'technologies': project.get_technologies_list(),
        'tools': project.get_tools_list(),
        'gallery_images': project.get_gallery_images_list(),
    }
    return render(request, 'projects/detail.html', context)
