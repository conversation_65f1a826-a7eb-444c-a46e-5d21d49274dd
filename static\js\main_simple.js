// ===== SIMPLE PORTFOLIO WEBSITE JAVASCRIPT =====

document.addEventListener("DOMContentLoaded", function () {
  
  // ===== NAVBAR SCROLL EFFECT =====
  const navbar = document.querySelector(".navbar");
  if (navbar) {
    window.addEventListener("scroll", function () {
      if (window.scrollY > 50) {
        navbar.classList.add("scrolled");
      } else {
        navbar.classList.remove("scrolled");
      }
    });
  }

  // ===== FADE IN ANIMATION =====
  const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -50px 0px",
  };

  const observer = new IntersectionObserver(function (entries) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add("visible");
      }
    });
  }, observerOptions);

  // Observe all elements with fade-in class
  const fadeElements = document.querySelectorAll(".fade-in");
  fadeElements.forEach((el) => observer.observe(el));

  // ===== SMOOTH SCROLLING =====
  const anchorLinks = document.querySelectorAll('a[href^="#"]');
  anchorLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      e.preventDefault();
      const targetId = this.getAttribute("href");
      const targetElement = document.querySelector(targetId);

      if (targetElement) {
        const offsetTop = targetElement.offsetTop - 80;
        window.scrollTo({
          top: offsetTop,
          behavior: "smooth",
        });
      }
    });
  });

  // ===== NAVBAR COLLAPSE ON MOBILE =====
  const navbarToggler = document.querySelector(".navbar-toggler");
  const navbarCollapse = document.querySelector(".navbar-collapse");
  const navLinks = document.querySelectorAll(".nav-link");

  navLinks.forEach((link) => {
    link.addEventListener("click", function () {
      if (window.innerWidth < 992) {
        navbarCollapse.classList.remove("show");
      }
    });
  });

  // ===== DARK MODE TOGGLE =====
  const themeToggle = document.getElementById("themeToggle");
  const themeIcon = document.getElementById("themeIcon");
  const body = document.body;

  // Check for saved theme preference or default to light mode
  const currentTheme = localStorage.getItem("theme") || "light";
  body.setAttribute("data-theme", currentTheme);

  // Update icon based on current theme
  if (currentTheme === "dark") {
    if (themeIcon) themeIcon.className = "fas fa-sun";
  } else {
    if (themeIcon) themeIcon.className = "fas fa-moon";
  }

  // Theme toggle functionality
  if (themeToggle) {
    themeToggle.addEventListener("click", function () {
      const currentTheme = body.getAttribute("data-theme");
      const newTheme = currentTheme === "dark" ? "light" : "dark";

      body.setAttribute("data-theme", newTheme);
      localStorage.setItem("theme", newTheme);

      // Update icon
      if (themeIcon) {
        if (newTheme === "dark") {
          themeIcon.className = "fas fa-sun";
        } else {
          themeIcon.className = "fas fa-moon";
        }
      }
    });
  }

  // ===== CONTACT FORM ENHANCEMENT =====
  const contactForm = document.querySelector("#contact-form");
  if (contactForm) {
    contactForm.addEventListener("submit", function (e) {
      const submitBtn = this.querySelector('button[type="submit"]');
      if (submitBtn) {
        const originalText = submitBtn.textContent;
        submitBtn.textContent = "Sending...";
        submitBtn.disabled = true;

        // Re-enable button after 3 seconds
        setTimeout(() => {
          submitBtn.textContent = originalText;
          submitBtn.disabled = false;
        }, 3000);
      }
    });
  }

  // ===== ANIMATED COUNTERS =====
  const counters = document.querySelectorAll(".stat-number");
  counters.forEach((counter) => {
    const target = parseInt(counter.getAttribute("data-target"));
    if (target) {
      const increment = target / 100;
      let current = 0;

      const updateCounter = function () {
        if (current < target) {
          current += increment;
          counter.textContent = Math.ceil(current);
          setTimeout(updateCounter, 20);
        } else {
          counter.textContent = target;
        }
      };

      // Start animation when element is visible
      const counterObserver = new IntersectionObserver(
        function (entries) {
          entries.forEach((entry) => {
            if (entry.isIntersecting && !counter.classList.contains("animated")) {
              counter.classList.add("animated");
              updateCounter();
            }
          });
        },
        { threshold: 0.5 }
      );

      counterObserver.observe(counter);
    }
  });

  // ===== SKILL BARS ANIMATION =====
  const skillBars = document.querySelectorAll(".skill-progress");
  skillBars.forEach((bar) => {
    const skillObserver = new IntersectionObserver(
      function (entries) {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !bar.classList.contains("animated")) {
            bar.classList.add("animated");
            const width = bar.getAttribute("data-width");
            if (width) {
              setTimeout(() => {
                bar.style.width = width + "%";
              }, 200);
            }
          }
        });
      },
      { threshold: 0.5 }
    );

    skillObserver.observe(bar);
  });

  // ===== PRELOADER =====
  const preloader = document.querySelector(".preloader");
  if (preloader) {
    window.addEventListener("load", function () {
      preloader.style.opacity = "0";
      setTimeout(() => {
        preloader.style.display = "none";
      }, 500);
    });
  }

  // ===== SCROLL TO TOP BUTTON =====
  const scrollTopBtn = document.querySelector(".scroll-top");
  if (scrollTopBtn) {
    window.addEventListener("scroll", function () {
      if (window.pageYOffset > 300) {
        scrollTopBtn.classList.add("visible");
      } else {
        scrollTopBtn.classList.remove("visible");
      }
    });

    scrollTopBtn.addEventListener("click", function () {
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    });
  }

  console.log("Portfolio website loaded successfully!");
});
