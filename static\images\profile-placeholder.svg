<svg width="300" height="300" viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#50C878;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFD700;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="150" cy="150" r="150" fill="url(#bg)"/>
  
  <!-- Profile Icon -->
  <circle cx="150" cy="120" r="40" fill="white" opacity="0.9"/>
  <path d="M150 170 C120 170, 100 190, 100 220 L200 220 C200 190, 180 170, 150 170 Z" fill="white" opacity="0.9"/>
  
  <!-- Decorative Elements -->
  <circle cx="80" cy="80" r="3" fill="white" opacity="0.6"/>
  <circle cx="220" cy="100" r="2" fill="white" opacity="0.6"/>
  <circle cx="250" cy="200" r="3" fill="white" opacity="0.6"/>
  <circle cx="70" cy="220" r="2" fill="white" opacity="0.6"/>
  
  <!-- Text -->
  <text x="150" y="270" text-anchor="middle" fill="white" font-family="serif" font-size="16" font-weight="bold">Muhammad Trinanda</text>
</svg>
