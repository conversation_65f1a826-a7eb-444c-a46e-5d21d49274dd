/* ===== VARIABLES ===== */
:root {
  --emerald-green: #50c878;
  --gold: #ffd700;
  --dark-emerald: #3a9b5c;
  --light-gold: #fff8dc;
  --dark-bg: #1a1a1a;
  --light-bg: #f8f9fa;
  --text-dark: #2c3e50;
  --text-light: #6c757d;
  --white: #ffffff;

  /* Fonts */
  --font-heading: "Playfair Display", serif;
  --font-body: "Lato", sans-serif;
}

/* ===== GLOBAL STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-body);
  line-height: 1.6;
  color: var(--text-dark);
  background-color: var(--white);
  overflow-x: hidden;
}

html {
  scroll-behavior: smooth;
}

/* ===== TYPOGRAPHY ===== */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
}

.vintage-heading {
  font-family: var(--font-heading);
  color: var(--emerald-green);
  position: relative;
}

.vintage-heading::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 50px;
  height: 2px;
  background: linear-gradient(90deg, var(--gold), var(--emerald-green));
}

/* ===== NAVIGATION ===== */
.elegant-nav {
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  padding: 1rem 0;
}

.elegant-nav.scrolled {
  background: rgba(26, 26, 26, 0.98);
  padding: 0.5rem 0;
}

.vintage-logo .brand-name {
  font-family: var(--font-heading);
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(45deg, var(--emerald-green), var(--gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar-nav .nav-link {
  font-family: var(--font-body);
  font-weight: 500;
  color: var(--white) !important;
  margin: 0 0.5rem;
  padding: 0.5rem 1rem !important;
  border-radius: 25px;
  transition: all 0.3s ease;
  position: relative;
}

.navbar-nav .nav-link:hover {
  background: linear-gradient(45deg, var(--emerald-green), var(--gold));
  transform: translateY(-2px);
}

/* ===== HERO SECTION ===== */
.hero-section {
  min-height: 100vh;
  background: linear-gradient(
    135deg,
    var(--dark-bg) 0%,
    var(--dark-emerald) 100%
  );
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23FFD700" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 2;
  color: var(--white);
  text-align: center;
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, var(--white), var(--gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: var(--light-gold);
  font-weight: 300;
}

.hero-tagline {
  font-size: 1.2rem;
  margin-bottom: 3rem;
  color: rgba(255, 255, 255, 0.9);
}

/* ===== BUTTONS ===== */
.btn-vintage {
  background: linear-gradient(45deg, var(--emerald-green), var(--gold));
  border: none;
  color: var(--white);
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  position: relative;
  overflow: hidden;
}

.btn-vintage:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(80, 200, 120, 0.3);
  color: var(--white);
}

.btn-vintage::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-vintage:hover::before {
  left: 100%;
}

/* ===== SECTIONS ===== */
.section-padding {
  padding: 5rem 0;
}

.section-title {
  font-size: 3rem;
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 3px;
  background: linear-gradient(90deg, var(--emerald-green), var(--gold));
}

/* ===== CARDS ===== */
.vintage-card {
  background: var(--white);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(80, 200, 120, 0.1);
  height: 100%;
}

.vintage-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 50px rgba(80, 200, 120, 0.2);
}

/* ===== FOOTER ===== */
.elegant-footer {
  background: var(--dark-bg);
  color: var(--white);
}

.social-links {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: linear-gradient(45deg, var(--emerald-green), var(--gold));
  color: var(--white);
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1.2rem;
}

.social-link:hover {
  transform: translateY(-3px) scale(1.1);
  color: var(--white);
}

/* ===== ANIMATIONS ===== */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* ===== SCROLL TO TOP BUTTON ===== */
.scroll-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: linear-gradient(45deg, var(--emerald-green), var(--gold));
  color: var(--white);
  border: none;
  border-radius: 50%;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
  z-index: 1000;
  box-shadow: 0 4px 15px rgba(80, 200, 120, 0.3);
}

.scroll-top.visible {
  opacity: 1;
  visibility: visible;
}

.scroll-top:hover {
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 6px 20px rgba(80, 200, 120, 0.4);
}

/* ===== LAZY LOADING IMAGES ===== */
img.lazy {
  opacity: 0;
  transition: opacity 0.3s;
}

img.lazy.loaded {
  opacity: 1;
}

/* ===== PRELOADER ===== */
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--dark-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.5s ease;
}

.preloader-content {
  text-align: center;
  color: var(--white);
}

.preloader-logo {
  font-family: var(--font-heading);
  font-size: 2rem;
  background: linear-gradient(45deg, var(--emerald-green), var(--gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
}

.preloader-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(80, 200, 120, 0.3);
  border-top: 3px solid var(--emerald-green);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* ===== ENHANCED ANIMATIONS ===== */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.6s ease forwards;
}

.slide-in-right {
  animation: slideInRight 0.6s ease forwards;
}

.slide-in-up {
  animation: slideInUp 0.6s ease forwards;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 1200px) {
  .hero-title {
    font-size: 3.5rem;
  }

  .section-padding {
    padding: 4rem 0;
  }
}

@media (max-width: 992px) {
  .hero-title {
    font-size: 3rem;
  }

  .hero-subtitle {
    font-size: 1.3rem;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .vintage-card {
    margin-bottom: 2rem;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .hero-tagline {
    font-size: 1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-padding {
    padding: 3rem 0;
  }

  .social-links {
    justify-content: center;
    margin-top: 1rem;
  }

  .navbar-nav .nav-link {
    margin: 0.2rem 0;
    text-align: center;
  }

  .vintage-heading::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .scroll-top {
    bottom: 20px;
    right: 20px;
    width: 45px;
    height: 45px;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .btn-vintage {
    padding: 0.8rem 2rem;
    font-size: 1rem;
  }

  .vintage-card {
    padding: 1.5rem;
  }

  .elegant-nav {
    padding: 0.5rem 0;
  }

  .brand-name {
    font-size: 1.2rem !important;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .elegant-nav,
  .scroll-top,
  .preloader {
    display: none !important;
  }

  .hero-section {
    background: white !important;
    color: black !important;
  }

  .vintage-heading {
    color: black !important;
  }

  .btn-vintage {
    border: 2px solid black !important;
    background: white !important;
    color: black !important;
  }
}
