{% extends 'base.html' %} {% load static %} {% block title %}{{ page_title }}{%
endblock %} {% block description %}{{ meta_description }}{% endblock %} {% block
content %}
<!-- About Hero Section -->
<section
  class="section-padding"
  style="
    background: linear-gradient(
      135deg,
      var(--dark-bg) 0%,
      var(--dark-emerald) 100%
    );
    color: white;
    margin-top: 80px;
  "
>
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-6">
        <div class="fade-in">
          <h1 class="vintage-heading" style="color: white; font-size: 3rem">
            About Muhammad Trinanda
          </h1>
          <p class="lead" style="color: var(--light-gold)">
            A passionate freelancer dedicated to creating exceptional digital
            experiences and providing expert financial solutions.
          </p>
        </div>
      </div>
      <div class="col-lg-6 text-center">
        <div class="fade-in">
          <div class="profile-image-container">
            <img
              src="{% static 'images/profile-placeholder.svg' %}"
              alt="Muhammad Trinanda Profile"
              class="img-fluid rounded-circle profile-image"
              style="
                width: 300px;
                height: 300px;
                object-fit: cover;
                border: 5px solid var(--gold);
              "
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Biography Section -->
<section class="section-padding">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="fade-in">
          <h2 class="section-title vintage-heading">My Story</h2>
          <div class="row">
            <div class="col-lg-8 mx-auto">
              <p class="lead text-center mb-5">
                Welcome to my creative world where design meets functionality,
                and precision meets innovation.
              </p>

              <div class="biography-content">
                <p>
                  I'm Muhammad Trinanda, a versatile freelancer with a unique
                  blend of creative and analytical skills. My journey began with
                  a passion for visual storytelling through graphic design,
                  which naturally evolved into the digital realm of web
                  development. Along the way, I discovered my aptitude for
                  financial systems and became an expert in Accurate accounting
                  software.
                </p>

                <p>
                  What sets me apart is my ability to bridge the gap between
                  creativity and functionality. Whether I'm designing a brand
                  identity that captures the essence of a business, developing a
                  web application that delivers seamless user experiences, or
                  optimizing financial processes for maximum efficiency, I bring
                  the same level of dedication and attention to detail to every
                  project.
                </p>

                <p>
                  My approach is rooted in understanding each client's unique
                  needs and translating them into elegant, practical solutions.
                  I believe that great design should not only look beautiful but
                  also serve a purpose, and that technology should empower
                  businesses to achieve their goals more effectively.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Skills & Expertise Section -->
<section class="section-padding" style="background-color: var(--light-bg)">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <h2 class="section-title vintage-heading fade-in">
          Skills & Expertise
        </h2>
      </div>
    </div>
    <div class="row g-4">
      <!-- Design Skills -->
      <div class="col-lg-4">
        <div class="vintage-card fade-in">
          <h4 class="vintage-heading">
            <i
              class="fas fa-palette me-2"
              style="color: var(--emerald-green)"
            ></i>
            Design Skills
          </h4>
          <ul class="skills-list">
            <li>Adobe Creative Suite (Photoshop, Illustrator, InDesign)</li>
            <li>Brand Identity & Logo Design</li>
            <li>Print & Digital Marketing Materials</li>
            <li>UI/UX Design Principles</li>
            <li>Typography & Color Theory</li>
            <li>Layout & Composition</li>
          </ul>
        </div>
      </div>

      <!-- Technical Skills -->
      <div class="col-lg-4">
        <div class="vintage-card fade-in">
          <h4 class="vintage-heading">
            <i class="fas fa-code me-2" style="color: var(--gold)"></i>
            Technical Skills
          </h4>
          <ul class="skills-list">
            <li>Python & Django Framework</li>
            <li>HTML5, CSS3, JavaScript</li>
            <li>Bootstrap & Responsive Design</li>
            <li>Database Design & Management</li>
            <li>API Development & Integration</li>
            <li>Version Control (Git)</li>
          </ul>
        </div>
      </div>

      <!-- Finance Skills -->
      <div class="col-lg-4">
        <div class="vintage-card fade-in">
          <h4 class="vintage-heading">
            <i
              class="fas fa-calculator me-2"
              style="color: var(--emerald-green)"
            ></i>
            Finance Skills
          </h4>
          <ul class="skills-list">
            <li>Accurate Accounting Software</li>
            <li>Financial Reporting & Analysis</li>
            <li>Bookkeeping & Record Management</li>
            <li>Process Automation</li>
            <li>Tax Preparation Support</li>
            <li>Financial System Optimization</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Values & Philosophy Section -->
<section class="section-padding">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="fade-in">
          <h2 class="section-title vintage-heading">My Values & Philosophy</h2>
          <div class="row g-4">
            <div class="col-md-6">
              <div class="value-item">
                <div class="value-icon">
                  <i
                    class="fas fa-star"
                    style="color: var(--gold); font-size: 2rem"
                  ></i>
                </div>
                <h4>Excellence</h4>
                <p>
                  I strive for perfection in every project, ensuring that each
                  deliverable meets the highest standards of quality and
                  craftsmanship.
                </p>
              </div>
            </div>

            <div class="col-md-6">
              <div class="value-item">
                <div class="value-icon">
                  <i
                    class="fas fa-handshake"
                    style="color: var(--emerald-green); font-size: 2rem"
                  ></i>
                </div>
                <h4>Collaboration</h4>
                <p>
                  I believe in working closely with clients as partners,
                  ensuring clear communication and shared vision throughout the
                  project lifecycle.
                </p>
              </div>
            </div>

            <div class="col-md-6">
              <div class="value-item">
                <div class="value-icon">
                  <i
                    class="fas fa-lightbulb"
                    style="color: var(--gold); font-size: 2rem"
                  ></i>
                </div>
                <h4>Innovation</h4>
                <p>
                  I stay current with industry trends and technologies, bringing
                  fresh perspectives and creative solutions to every challenge.
                </p>
              </div>
            </div>

            <div class="col-md-6">
              <div class="value-item">
                <div class="value-icon">
                  <i
                    class="fas fa-clock"
                    style="color: var(--emerald-green); font-size: 2rem"
                  ></i>
                </div>
                <h4>Reliability</h4>
                <p>
                  I understand the importance of deadlines and deliverables,
                  consistently providing timely and dependable service to all my
                  clients.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Call to Action -->
<section
  class="section-padding"
  style="
    background: linear-gradient(
      135deg,
      var(--emerald-green) 0%,
      var(--gold) 100%
    );
    color: white;
  "
>
  <div class="container">
    <div class="row justify-content-center text-center">
      <div class="col-lg-8">
        <div class="fade-in">
          <h2 class="mb-4" style="color: white">
            Let's Create Something Amazing Together
          </h2>
          <p class="lead mb-4">
            Ready to bring your vision to life? I'm here to help you achieve
            your goals with creativity, expertise, and dedication.
          </p>
          <a href="{% url 'core:contact' %}" class="btn btn-light btn-lg"
            >Get In Touch</a
          >
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock %} {% block extra_css %}
<style>
  .profile-image {
    transition: transform 0.3s ease;
  }

  .profile-image:hover {
    transform: scale(1.05);
  }

  .biography-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    text-align: justify;
  }

  .skills-list {
    list-style: none;
    padding: 0;
  }

  .skills-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(80, 200, 120, 0.1);
    position: relative;
    padding-left: 1.5rem;
  }

  .skills-list li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--emerald-green);
    font-weight: bold;
  }

  .value-item {
    text-align: center;
    padding: 2rem 1rem;
  }

  .value-icon {
    margin-bottom: 1rem;
  }

  .value-item h4 {
    color: var(--emerald-green);
    margin-bottom: 1rem;
  }

  .profile-image-container {
    position: relative;
  }

  .profile-image-container::before {
    content: "";
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, var(--emerald-green), var(--gold));
    border-radius: 50%;
    z-index: -1;
    opacity: 0.3;
  }
</style>
{% endblock %}
