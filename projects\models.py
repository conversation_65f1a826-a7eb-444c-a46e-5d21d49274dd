from django.db import models
from django.urls import reverse
from django.utils.text import slugify


class ProjectCategory(models.Model):
    """Categories for projects (Graphic Design, Web Development, Finance)"""
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=100, unique=True, blank=True)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, default='fas fa-folder', help_text='Font Awesome icon class')
    color = models.CharField(max_length=7, default='#50C878', help_text='Hex color code')
    order = models.PositiveIntegerField(default=0, help_text='Display order')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Project Category'
        verbose_name_plural = 'Project Categories'
        ordering = ['order', 'name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)


class Project(models.Model):
    """Individual projects for the portfolio"""
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('published', 'Published'),
        ('archived', 'Archived'),
    ]

    title = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True, blank=True)
    category = models.ForeignKey(ProjectCategory, on_delete=models.CASCADE, related_name='projects')
    description = models.TextField()
    short_description = models.CharField(max_length=300, help_text='Brief description for cards')

    # Project details
    client = models.CharField(max_length=100, blank=True)
    project_date = models.DateField()
    project_url = models.URLField(blank=True, help_text='Live project URL')
    github_url = models.URLField(blank=True, help_text='GitHub repository URL')

    # Images
    featured_image = models.ImageField(upload_to='projects/featured/', blank=True)
    gallery_images = models.TextField(blank=True, help_text='Comma-separated image URLs or paths')

    # Technologies and tools
    technologies = models.CharField(max_length=500, blank=True, help_text='Comma-separated list of technologies')
    tools = models.CharField(max_length=500, blank=True, help_text='Comma-separated list of tools used')

    # SEO and metadata
    meta_description = models.CharField(max_length=160, blank=True)
    meta_keywords = models.CharField(max_length=200, blank=True)

    # Status and ordering
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    is_featured = models.BooleanField(default=False, help_text='Show on homepage')
    order = models.PositiveIntegerField(default=0, help_text='Display order within category')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Project'
        verbose_name_plural = 'Projects'
        ordering = ['-is_featured', 'order', '-project_date']

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('projects:detail', kwargs={'slug': self.slug})

    def get_technologies_list(self):
        """Return technologies as a list"""
        if self.technologies:
            return [tech.strip() for tech in self.technologies.split(',')]
        return []

    def get_tools_list(self):
        """Return tools as a list"""
        if self.tools:
            return [tool.strip() for tool in self.tools.split(',')]
        return []

    def get_gallery_images_list(self):
        """Return gallery images as a list"""
        if self.gallery_images:
            return [img.strip() for img in self.gallery_images.split(',')]
        return []


class ProjectImage(models.Model):
    """Additional images for projects"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='projects/gallery/')
    caption = models.CharField(max_length=200, blank=True)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order', 'created_at']

    def __str__(self):
        return f"{self.project.title} - Image {self.order}"


class Testimonial(models.Model):
    """Client testimonials"""
    name = models.CharField(max_length=100)
    company = models.CharField(max_length=100, blank=True)
    position = models.CharField(max_length=100, blank=True)
    content = models.TextField()
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='testimonials', blank=True, null=True)
    rating = models.PositiveIntegerField(default=5, choices=[(i, i) for i in range(1, 6)])
    is_featured = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-is_featured', '-created_at']

    def __str__(self):
        return f"{self.name} - {self.company}"
