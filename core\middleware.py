"""
Custom middleware for performance optimization
"""

from django.utils.cache import add_never_cache_headers
from django.http import HttpResponse
import gzip
import io


class CacheControlMiddleware:
    """
    Middleware to add cache control headers for static content
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # Add cache headers for static files
        if request.path.startswith('/static/'):
            response['Cache-Control'] = 'public, max-age=31536000'  # 1 year
            response['Expires'] = 'Thu, 31 Dec 2025 23:59:59 GMT'
        
        # Add cache headers for media files
        elif request.path.startswith('/media/'):
            response['Cache-Control'] = 'public, max-age=86400'  # 1 day
        
        # No cache for admin and dynamic pages
        elif request.path.startswith('/admin/') or request.method == 'POST':
            add_never_cache_headers(response)
        
        # Cache for regular pages
        else:
            response['Cache-Control'] = 'public, max-age=3600'  # 1 hour
        
        return response


class SecurityHeadersMiddleware:
    """
    Middleware to add security headers
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # Security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
        
        # Content Security Policy
        csp = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com https://cdnjs.cloudflare.com; "
            "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; "
            "img-src 'self' data: https:; "
            "connect-src 'self'; "
            "frame-ancestors 'none';"
        )
        response['Content-Security-Policy'] = csp
        
        return response


class CompressionMiddleware:
    """
    Simple gzip compression middleware
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # Only compress if client accepts gzip
        if 'gzip' not in request.META.get('HTTP_ACCEPT_ENCODING', ''):
            return response
        
        # Only compress text content
        content_type = response.get('Content-Type', '')
        if not any(ct in content_type for ct in ['text/', 'application/json', 'application/javascript']):
            return response
        
        # Don't compress if already compressed
        if response.get('Content-Encoding'):
            return response
        
        # Don't compress small responses
        if len(response.content) < 200:
            return response
        
        # Compress the content
        try:
            compressed_content = gzip.compress(response.content)
            response.content = compressed_content
            response['Content-Encoding'] = 'gzip'
            response['Content-Length'] = str(len(compressed_content))
        except Exception:
            # If compression fails, return original response
            pass
        
        return response


class PerformanceMiddleware:
    """
    Middleware to add performance-related headers
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # Add performance hints
        if request.path == '/':
            # Preload critical resources
            response['Link'] = (
                '</static/css/style.css>; rel=preload; as=style, '
                '</static/js/main.js>; rel=preload; as=script, '
                '<https://fonts.googleapis.com>; rel=preconnect, '
                '<https://fonts.gstatic.com>; rel=preconnect; crossorigin'
            )
        
        # Add timing headers for monitoring
        response['Server-Timing'] = 'django'
        
        return response
