"""
Custom middleware for performance optimization
"""

from django.utils.cache import add_never_cache_headers
from django.http import HttpResponse
import gzip
import io


class CacheControlMiddleware:
    """
    Middleware to add cache control headers for static content
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # Add cache headers for static files
        if request.path.startswith('/static/'):
            response['Cache-Control'] = 'public, max-age=31536000'  # 1 year
            response['Expires'] = 'Thu, 31 Dec 2025 23:59:59 GMT'
        
        # Add cache headers for media files
        elif request.path.startswith('/media/'):
            response['Cache-Control'] = 'public, max-age=86400'  # 1 day
        
        # No cache for admin and dynamic pages
        elif request.path.startswith('/admin/') or request.method == 'POST':
            add_never_cache_headers(response)
        
        # Cache for regular pages
        else:
            response['Cache-Control'] = 'public, max-age=3600'  # 1 hour
        
        return response


class SecurityHeadersMiddleware:
    """
    Enhanced middleware to add comprehensive security headers
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        # Basic security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'

        # Enhanced Permissions Policy
        permissions_policy = (
            "geolocation=(), "
            "microphone=(), "
            "camera=(), "
            "payment=(), "
            "usb=(), "
            "magnetometer=(), "
            "gyroscope=(), "
            "speaker=(), "
            "vibrate=(), "
            "fullscreen=(self), "
            "sync-xhr=()"
        )
        response['Permissions-Policy'] = permissions_policy

        # Content Security Policy with analytics support
        csp_directives = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://www.googletagmanager.com https://www.google-analytics.com https://connect.facebook.net https://snap.licdn.com",
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com https://cdnjs.cloudflare.com",
            "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com",
            "img-src 'self' data: https: https://www.google-analytics.com https://www.facebook.com https://px.ads.linkedin.com",
            "connect-src 'self' https://www.google-analytics.com https://analytics.google.com https://www.facebook.com",
            "frame-ancestors 'none'",
            "base-uri 'self'",
            "form-action 'self'",
            "upgrade-insecure-requests"
        ]

        # Only add CSP in production or when explicitly enabled
        if not getattr(request, 'DEBUG', True):
            response['Content-Security-Policy'] = "; ".join(csp_directives)

        # HSTS (only in production with HTTPS)
        if request.is_secure():
            response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload'

        # Additional security headers
        response['Cross-Origin-Embedder-Policy'] = 'require-corp'
        response['Cross-Origin-Opener-Policy'] = 'same-origin'
        response['Cross-Origin-Resource-Policy'] = 'same-origin'

        return response


class RateLimitMiddleware:
    """
    Simple rate limiting middleware for contact forms and sensitive endpoints
    """

    def __init__(self, get_response):
        self.get_response = get_response
        self.rate_limit_cache = {}
        self.cleanup_interval = 3600  # Clean cache every hour
        self.last_cleanup = 0

    def __call__(self, request):
        import time

        # Clean old entries periodically
        current_time = time.time()
        if current_time - self.last_cleanup > self.cleanup_interval:
            self._cleanup_cache(current_time)
            self.last_cleanup = current_time

        # Check rate limits for specific paths
        if self._should_rate_limit(request):
            client_ip = self._get_client_ip(request)

            if self._is_rate_limited(client_ip, request.path):
                from django.http import HttpResponseTooManyRequests
                return HttpResponseTooManyRequests("Rate limit exceeded. Please try again later.")

        response = self.get_response(request)
        return response

    def _should_rate_limit(self, request):
        """Check if this request should be rate limited"""
        rate_limited_paths = ['/contact/', '/admin/login/']
        return any(request.path.startswith(path) for path in rate_limited_paths)

    def _get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def _is_rate_limited(self, client_ip, path):
        """Check if client is rate limited"""
        import time

        current_time = time.time()
        key = f"{client_ip}:{path}"

        if key not in self.rate_limit_cache:
            self.rate_limit_cache[key] = []

        # Remove old requests (older than 1 hour)
        self.rate_limit_cache[key] = [
            timestamp for timestamp in self.rate_limit_cache[key]
            if current_time - timestamp < 3600
        ]

        # Check limits
        if path == '/contact/':
            # Allow 5 contact form submissions per hour
            if len(self.rate_limit_cache[key]) >= 5:
                return True
        elif path.startswith('/admin/'):
            # Allow 10 admin login attempts per hour
            if len(self.rate_limit_cache[key]) >= 10:
                return True

        # Add current request
        self.rate_limit_cache[key].append(current_time)
        return False

    def _cleanup_cache(self, current_time):
        """Clean old entries from cache"""
        keys_to_remove = []
        for key, timestamps in self.rate_limit_cache.items():
            # Remove timestamps older than 2 hours
            self.rate_limit_cache[key] = [
                timestamp for timestamp in timestamps
                if current_time - timestamp < 7200
            ]
            # Remove empty entries
            if not self.rate_limit_cache[key]:
                keys_to_remove.append(key)

        for key in keys_to_remove:
            del self.rate_limit_cache[key]


class CompressionMiddleware:
    """
    Simple gzip compression middleware
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # Only compress if client accepts gzip
        if 'gzip' not in request.META.get('HTTP_ACCEPT_ENCODING', ''):
            return response
        
        # Only compress text content
        content_type = response.get('Content-Type', '')
        if not any(ct in content_type for ct in ['text/', 'application/json', 'application/javascript']):
            return response
        
        # Don't compress if already compressed
        if response.get('Content-Encoding'):
            return response
        
        # Don't compress small responses
        if len(response.content) < 200:
            return response
        
        # Compress the content
        try:
            compressed_content = gzip.compress(response.content)
            response.content = compressed_content
            response['Content-Encoding'] = 'gzip'
            response['Content-Length'] = str(len(compressed_content))
        except Exception:
            # If compression fails, return original response
            pass
        
        return response


class PerformanceMiddleware:
    """
    Middleware to add performance-related headers
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # Add performance hints
        if request.path == '/':
            # Preload critical resources
            response['Link'] = (
                '</static/css/style.css>; rel=preload; as=style, '
                '</static/js/main.js>; rel=preload; as=script, '
                '<https://fonts.googleapis.com>; rel=preconnect, '
                '<https://fonts.gstatic.com>; rel=preconnect; crossorigin'
            )
        
        # Add timing headers for monitoring
        response['Server-Timing'] = 'django'
        
        return response
