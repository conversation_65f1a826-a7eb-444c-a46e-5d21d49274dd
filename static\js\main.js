// ===== SMOOTH SCROLLING AND ANIMATIONS =====

document.addEventListener("DOMContentLoaded", function () {
  // ===== NAVBAR SCROLL EFFECT =====
  const navbar = document.querySelector(".elegant-nav");

  window.addEventListener("scroll", function () {
    if (window.scrollY > 50) {
      navbar.classList.add("scrolled");
    } else {
      navbar.classList.remove("scrolled");
    }
  });

  // ===== FADE IN ANIMATION ON SCROLL =====
  const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -50px 0px",
  };

  const observer = new IntersectionObserver(function (entries) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add("visible");
      }
    });
  }, observerOptions);

  // Observe all elements with fade-in class
  const fadeElements = document.querySelectorAll(".fade-in");
  fadeElements.forEach((el) => observer.observe(el));

  // ===== SMOOTH SCROLL FOR ANCHOR LINKS =====
  const anchorLinks = document.querySelectorAll('a[href^="#"]');

  anchorLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      e.preventDefault();

      const targetId = this.getAttribute("href");
      const targetElement = document.querySelector(targetId);

      if (targetElement) {
        const offsetTop = targetElement.offsetTop - 80; // Account for fixed navbar

        window.scrollTo({
          top: offsetTop,
          behavior: "smooth",
        });
      }
    });
  });

  // ===== ENHANCED MOBILE NAVIGATION =====
  const navbarToggler = document.querySelector(".navbar-toggler");
  const navbarCollapse = document.querySelector(".navbar-collapse");
  const navLinks = document.querySelectorAll(".nav-link");

  // Create mobile overlay
  const mobileOverlay = document.createElement("div");
  mobileOverlay.className = "mobile-menu-overlay";
  document.body.appendChild(mobileOverlay);

  // Toggle mobile menu
  if (navbarToggler) {
    navbarToggler.addEventListener("click", function () {
      setTimeout(() => {
        if (navbarCollapse.classList.contains("show")) {
          mobileOverlay.classList.add("show");
          document.body.style.overflow = "hidden";
        } else {
          mobileOverlay.classList.remove("show");
          document.body.style.overflow = "";
        }
      }, 100);
    });
  }

  // Close mobile menu when clicking overlay
  mobileOverlay.addEventListener("click", function () {
    navbarCollapse.classList.remove("show");
    mobileOverlay.classList.remove("show");
    document.body.style.overflow = "";
  });

  // Close mobile menu when clicking nav links
  navLinks.forEach((link) => {
    link.addEventListener("click", function () {
      if (window.innerWidth < 992) {
        navbarCollapse.classList.remove("show");
        mobileOverlay.classList.remove("show");
        document.body.style.overflow = "";
      }
    });
  });

  // ===== MOBILE TOUCH GESTURES =====
  let touchStartY = 0;
  let touchEndY = 0;

  // Swipe to close mobile menu
  if (navbarCollapse) {
    navbarCollapse.addEventListener("touchstart", function (e) {
      touchStartY = e.changedTouches[0].screenY;
    });

    navbarCollapse.addEventListener("touchend", function (e) {
      touchEndY = e.changedTouches[0].screenY;
      handleSwipe();
    });

    function handleSwipe() {
      if (touchEndY < touchStartY - 50) {
        // Swipe up - close menu
        navbarCollapse.classList.remove("show");
        mobileOverlay.classList.remove("show");
        document.body.style.overflow = "";
      }
    }
  }

  // ===== MOBILE VIEWPORT HEIGHT FIX =====
  function setMobileVH() {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty("--vh", `${vh}px`);
  }

  setMobileVH();
  window.addEventListener("resize", setMobileVH);
  window.addEventListener("orientationchange", () => {
    setTimeout(setMobileVH, 100);
  });

  // ===== ANALYTICS TRACKING =====
  // Track external links
  document.addEventListener("click", function (e) {
    const link = e.target.closest("a");
    if (link && link.href) {
      const url = new URL(link.href);
      const currentDomain = window.location.hostname;

      // Track external links
      if (url.hostname !== currentDomain && url.hostname !== "") {
        if (typeof trackExternalLink === "function") {
          trackExternalLink(link.href, link.textContent.trim());
        }
      }

      // Track specific actions
      if (link.href.includes("mailto:")) {
        if (typeof gtag === "function") {
          gtag("event", "email_click", {
            event_category: "Contact",
            event_label: link.href.replace("mailto:", ""),
          });
        }
      }

      if (link.href.includes("tel:")) {
        if (typeof gtag === "function") {
          gtag("event", "phone_click", {
            event_category: "Contact",
            event_label: link.href.replace("tel:", ""),
          });
        }
      }

      // Track social media clicks
      const socialPlatforms = [
        "linkedin",
        "github",
        "instagram",
        "whatsapp",
        "facebook",
        "twitter",
      ];
      socialPlatforms.forEach((platform) => {
        if (link.href.includes(platform)) {
          if (typeof gtag === "function") {
            gtag("event", "social_click", {
              event_category: "Social Media",
              event_label: platform,
            });
          }
        }
      });
    }
  });

  // Track form interactions
  const forms = document.querySelectorAll("form");
  forms.forEach((form) => {
    form.addEventListener("submit", function (e) {
      const formId = this.id || "unknown_form";
      if (typeof gtag === "function") {
        gtag("event", "form_submit", {
          event_category: "Forms",
          event_label: formId,
        });
      }
    });
  });

  // Track button clicks
  const buttons = document.querySelectorAll(
    ".btn-vintage, .btn-outline-light, .btn-outline-dark"
  );
  buttons.forEach((button) => {
    button.addEventListener("click", function (e) {
      const buttonText = this.textContent.trim();
      const buttonClass = this.className;

      if (typeof gtag === "function") {
        gtag("event", "button_click", {
          event_category: "UI Interaction",
          event_label: buttonText,
          custom_parameter_1: buttonClass,
        });
      }
    });
  });

  // Track portfolio project views
  const projectLinks = document.querySelectorAll(
    'a[href*="/projects/detail/"]'
  );
  projectLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      const projectTitle =
        this.querySelector("h4")?.textContent || this.textContent.trim();
      const categoryElement =
        this.closest(".project-card")?.querySelector(".category-badge");
      const category = categoryElement?.textContent || "Unknown";

      if (typeof trackPortfolioView === "function") {
        trackPortfolioView(projectTitle, category);
      }
    });
  });

  // Track service inquiries
  const serviceButtons = document.querySelectorAll('a[href*="/contact/"]');
  serviceButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      const serviceContext = this.closest(".service-cta-card, .vintage-card");
      const serviceName =
        serviceContext?.querySelector("h4, h5")?.textContent || "General";

      if (typeof trackServiceInquiry === "function") {
        trackServiceInquiry(serviceName);
      }
    });
  });

  // ===== TYPING EFFECT FOR HERO SECTION =====
  const typingElement = document.querySelector(".typing-effect");
  if (typingElement) {
    const text = typingElement.textContent;
    typingElement.textContent = "";

    let i = 0;
    const typeWriter = function () {
      if (i < text.length) {
        typingElement.textContent += text.charAt(i);
        i++;
        setTimeout(typeWriter, 100);
      }
    };

    setTimeout(typeWriter, 1000);
  }

  // ===== LAZY LOADING FOR IMAGES =====
  const images = document.querySelectorAll("img[data-src]");

  const imageObserver = new IntersectionObserver(function (entries) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.classList.remove("lazy");
        imageObserver.unobserve(img);
      }
    });
  });

  images.forEach((img) => imageObserver.observe(img));

  // ===== CONTACT FORM ENHANCEMENT =====
  const contactForm = document.querySelector("#contact-form");
  if (contactForm) {
    contactForm.addEventListener("submit", function (e) {
      const submitBtn = this.querySelector('button[type="submit"]');
      const originalText = submitBtn.textContent;

      submitBtn.textContent = "Sending...";
      submitBtn.disabled = true;

      // Re-enable button after form submission (whether success or error)
      setTimeout(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
      }, 3000);
    });
  }

  // ===== PARALLAX EFFECT FOR HERO SECTION =====
  const heroSection = document.querySelector(".hero-section");
  if (heroSection) {
    window.addEventListener("scroll", function () {
      const scrolled = window.pageYOffset;
      const rate = scrolled * -0.5;

      if (scrolled < heroSection.offsetHeight) {
        heroSection.style.transform = `translateY(${rate}px)`;
      }
    });
  }

  // ===== ACTIVE NAV LINK HIGHLIGHTING =====
  const sections = document.querySelectorAll("section[id]");
  const navLinksArray = Array.from(document.querySelectorAll(".nav-link"));

  window.addEventListener("scroll", function () {
    let current = "";

    sections.forEach((section) => {
      const sectionTop = section.offsetTop - 100;
      const sectionHeight = section.offsetHeight;

      if (
        window.pageYOffset >= sectionTop &&
        window.pageYOffset < sectionTop + sectionHeight
      ) {
        current = section.getAttribute("id");
      }
    });

    navLinksArray.forEach((link) => {
      link.classList.remove("active");
      if (link.getAttribute("href") === `#${current}`) {
        link.classList.add("active");
      }
    });
  });

  // ===== ANIMATED COUNTERS =====
  const animateCounters = function () {
    const counters = document.querySelectorAll(".stat-number");

    counters.forEach((counter) => {
      const target = parseInt(counter.getAttribute("data-target"));
      const increment = target / 100;
      let current = 0;

      const updateCounter = function () {
        if (current < target) {
          current += increment;
          counter.textContent = Math.ceil(current);
          setTimeout(updateCounter, 20);
        } else {
          counter.textContent = target;
        }
      };

      // Start animation when element is visible
      const counterObserver = new IntersectionObserver(
        function (entries) {
          entries.forEach((entry) => {
            if (
              entry.isIntersecting &&
              !counter.classList.contains("animated")
            ) {
              counter.classList.add("animated");
              updateCounter();
            }
          });
        },
        { threshold: 0.5 }
      );

      counterObserver.observe(counter);
    });
  };

  // Initialize counters
  animateCounters();

  // ===== ANIMATED SKILL BARS =====
  const animateSkillBars = function () {
    const skillBars = document.querySelectorAll(".skill-progress");

    skillBars.forEach((bar) => {
      const skillObserver = new IntersectionObserver(
        function (entries) {
          entries.forEach((entry) => {
            if (entry.isIntersecting && !bar.classList.contains("animated")) {
              bar.classList.add("animated");
              const width = bar.getAttribute("data-width");
              setTimeout(() => {
                bar.style.width = width + "%";
              }, 200);
            }
          });
        },
        { threshold: 0.5 }
      );

      skillObserver.observe(bar);
    });
  };

  // Initialize skill bars
  animateSkillBars();

  // ===== DARK MODE TOGGLE =====
  const themeToggle = document.getElementById("themeToggle");
  const themeIcon = document.getElementById("themeIcon");
  const body = document.body;

  // Check for saved theme preference or default to light mode
  const currentTheme = localStorage.getItem("theme") || "light";
  body.setAttribute("data-theme", currentTheme);

  // Update icon based on current theme
  if (currentTheme === "dark") {
    themeIcon.className = "fas fa-sun";
  } else {
    themeIcon.className = "fas fa-moon";
  }

  // Theme toggle functionality
  if (themeToggle) {
    themeToggle.addEventListener("click", function () {
      const currentTheme = body.getAttribute("data-theme");
      const newTheme = currentTheme === "dark" ? "light" : "dark";

      body.setAttribute("data-theme", newTheme);
      localStorage.setItem("theme", newTheme);

      // Update icon with animation
      themeIcon.style.transform = "scale(0)";
      setTimeout(() => {
        if (newTheme === "dark") {
          themeIcon.className = "fas fa-sun";
        } else {
          themeIcon.className = "fas fa-moon";
        }
        themeIcon.style.transform = "scale(1)";
      }, 150);
    });
  }

  // ===== PRELOADER (if exists) =====
  const preloader = document.querySelector(".preloader");
  if (preloader) {
    window.addEventListener("load", function () {
      preloader.style.opacity = "0";
      setTimeout(() => {
        preloader.style.display = "none";
      }, 500);
    });
  }

  // ===== SCROLL TO TOP BUTTON =====
  const scrollTopBtn = document.querySelector(".scroll-top");
  if (scrollTopBtn) {
    window.addEventListener("scroll", function () {
      if (window.pageYOffset > 300) {
        scrollTopBtn.classList.add("visible");
      } else {
        scrollTopBtn.classList.remove("visible");
      }
    });

    scrollTopBtn.addEventListener("click", function () {
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    });
  }
});

// ===== UTILITY FUNCTIONS =====

// Debounce function for performance optimization
function debounce(func, wait, immediate) {
  let timeout;
  return function executedFunction() {
    const context = this;
    const args = arguments;
    const later = function () {
      timeout = null;
      if (!immediate) func.apply(context, args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func.apply(context, args);
  };
}

// Throttle function for scroll events
function throttle(func, limit) {
  let inThrottle;
  return function () {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}
