from django.shortcuts import render, redirect
from django.contrib import messages
from django.core.mail import EmailMessage
from django.conf import settings
from django.http import HttpResponse
from django.urls import reverse
from .forms import ContactForm


def home(request):
    """Home page view with hero section"""
    context = {
        'page_title': 'Home - Muhammad Trinanda',
        'meta_description': '<PERSON> - Professional Freelance Graphic Designer, Web Developer, and Finance Specialist. Elegant vintage portfolio showcasing creative expertise.',
    }
    return render(request, 'core/home.html', context)


def about(request):
    """About page view with biography and background"""
    context = {
        'page_title': 'About - Muhammad Trinanda',
        'meta_description': 'Learn about <PERSON>\'s background in graphic design, web development, and finance specializing in Accurate accounting software.',
    }
    return render(request, 'core/about.html', context)


def contact(request):
    """Contact page view with contact form"""
    if request.method == 'POST':
        form = ContactForm(request.POST)
        if form.is_valid():
            # Get form data
            name = form.cleaned_data['name']
            email = form.cleaned_data['email']
            subject = form.cleaned_data['subject']
            message = form.cleaned_data['message']

            # Create email message
            email_subject = f"Portfolio Contact: {subject}"
            email_body = f"""
            New contact form submission from your portfolio website:

            Name: {name}
            Email: {email}
            Subject: {subject}

            Message:
            {message}

            ---
            This message was sent from your portfolio contact form.
            """

            try:
                # Send email
                email_message = EmailMessage(
                    subject=email_subject,
                    body=email_body,
                    from_email=settings.DEFAULT_FROM_EMAIL if hasattr(settings, 'DEFAULT_FROM_EMAIL') else '<EMAIL>',
                    to=['<EMAIL>'],
                    reply_to=[email],
                )
                email_message.send()

                messages.success(request, 'Thank you for your message! I will get back to you soon.')
                return redirect('core:contact')

            except Exception as e:
                messages.error(request, 'Sorry, there was an error sending your message. Please try again or contact me directly.')
    else:
        form = ContactForm()

    context = {
        'form': form,
        'page_title': 'Contact - Muhammad Trinanda',
        'meta_description': 'Get in touch with Muhammad Trinanda for freelance graphic design, web development, or finance consulting projects.',
    }
    return render(request, 'core/contact.html', context)


def sitemap(request):
    """Generate XML sitemap"""
    sitemap_xml = '''<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc>https://muhammadtrinanda.com/</loc>
        <lastmod>2025-06-18</lastmod>
        <changefreq>monthly</changefreq>
        <priority>1.0</priority>
    </url>
    <url>
        <loc>https://muhammadtrinanda.com/about/</loc>
        <lastmod>2025-06-18</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>
    <url>
        <loc>https://muhammadtrinanda.com/projects/</loc>
        <lastmod>2025-06-18</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.9</priority>
    </url>
    <url>
        <loc>https://muhammadtrinanda.com/contact/</loc>
        <lastmod>2025-06-18</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.7</priority>
    </url>
</urlset>'''

    return HttpResponse(sitemap_xml, content_type='application/xml')


def robots_txt(request):
    """Serve robots.txt"""
    robots_content = '''User-agent: *
Allow: /

# Sitemap
Sitemap: https://muhammadtrinanda.com/sitemap.xml

# Disallow admin and private areas
Disallow: /admin/
Disallow: /media/private/

# Allow important pages
Allow: /
Allow: /about/
Allow: /projects/
Allow: /contact/
Allow: /static/'''

    return HttpResponse(robots_content, content_type='text/plain')
