<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="heroPattern" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse">
      <!-- Geometric pattern -->
      <circle cx="25" cy="25" r="1" fill="#FFD700" opacity="0.1"/>
      <circle cx="10" cy="10" r="0.5" fill="#50C878" opacity="0.15"/>
      <circle cx="40" cy="15" r="0.8" fill="#FFD700" opacity="0.08"/>
      <circle cx="15" cy="40" r="0.6" fill="#50C878" opacity="0.12"/>
      <circle cx="35" cy="35" r="0.4" fill="#FFD700" opacity="0.1"/>
      
      <!-- Lines -->
      <path d="M0 25 Q25 20 50 25" stroke="#50C878" stroke-width="0.5" fill="none" opacity="0.05"/>
      <path d="M25 0 Q30 25 25 50" stroke="#FFD700" stroke-width="0.3" fill="none" opacity="0.06"/>
    </pattern>
  </defs>
  
  <rect width="100" height="100" fill="url(#heroPattern)"/>
</svg>
