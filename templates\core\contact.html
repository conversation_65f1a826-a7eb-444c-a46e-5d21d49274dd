{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}
{% block description %}{{ meta_description }}{% endblock %}

{% block content %}
<!-- Contact Hero Section -->
<section class="section-padding" style="background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-emerald) 100%); color: white; margin-top: 80px;">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <div class="fade-in">
                    <h1 class="vintage-heading" style="color: white; font-size: 3rem;">Let's Work Together</h1>
                    <p class="lead" style="color: var(--light-gold);">
                        Ready to bring your vision to life? I'm here to help with your graphic design, 
                        web development, or finance needs.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Information Section -->
<section class="section-padding">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="row g-4">
                    <!-- Contact Info -->
                    <div class="col-lg-6">
                        <div class="fade-in">
                            <h2 class="vintage-heading mb-4">Get In Touch</h2>
                            <p class="lead text-muted mb-4">
                                I'm always excited to discuss new projects and opportunities. 
                                Whether you have a specific project in mind or just want to explore possibilities, 
                                I'd love to hear from you.
                            </p>
                            
                            <div class="contact-info">
                                <div class="contact-item">
                                    <div class="contact-icon">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="contact-details">
                                        <h5>Email</h5>
                                        <a href="mailto:<EMAIL>"><EMAIL></a>
                                    </div>
                                </div>
                                
                                <div class="contact-item">
                                    <div class="contact-icon">
                                        <i class="fab fa-linkedin"></i>
                                    </div>
                                    <div class="contact-details">
                                        <h5>LinkedIn</h5>
                                        <a href="https://www.linkedin.com/in/mtrinanda/" target="_blank">linkedin.com/in/mtrinanda</a>
                                    </div>
                                </div>
                                
                                <div class="contact-item">
                                    <div class="contact-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="contact-details">
                                        <h5>Response Time</h5>
                                        <p>Usually within 24 hours</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="social-links-contact mt-4">
                                <a href="mailto:<EMAIL>" class="social-link">
                                    <i class="fas fa-envelope"></i>
                                </a>
                                <a href="https://www.linkedin.com/in/mtrinanda/" target="_blank" class="social-link">
                                    <i class="fab fa-linkedin"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Contact Form -->
                    <div class="col-lg-6">
                        <div class="fade-in">
                            <div class="contact-form-container">
                                <h3 class="vintage-heading mb-4">Send a Message</h3>
                                
                                <!-- Display Messages -->
                                {% if messages %}
                                    {% for message in messages %}
                                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                            {{ message }}
                                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                        </div>
                                    {% endfor %}
                                {% endif %}
                                
                                <form method="post" id="contact-form" class="contact-form">
                                    {% csrf_token %}
                                    
                                    <div class="mb-3">
                                        <label for="{{ form.name.id_for_label }}" class="form-label">Name *</label>
                                        {{ form.name }}
                                        {% if form.name.errors %}
                                            <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="{{ form.email.id_for_label }}" class="form-label">Email *</label>
                                        {{ form.email }}
                                        {% if form.email.errors %}
                                            <div class="text-danger small">{{ form.email.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="{{ form.subject.id_for_label }}" class="form-label">Subject *</label>
                                        {{ form.subject }}
                                        {% if form.subject.errors %}
                                            <div class="text-danger small">{{ form.subject.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="{{ form.message.id_for_label }}" class="form-label">Message *</label>
                                        {{ form.message }}
                                        {% if form.message.errors %}
                                            <div class="text-danger small">{{ form.message.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                    
                                    <button type="submit" class="btn btn-vintage w-100">
                                        <i class="fas fa-paper-plane me-2"></i>Send Message
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services CTA Section -->
<section class="section-padding" style="background-color: var(--light-bg);">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="fade-in">
                    <h2 class="section-title vintage-heading">How Can I Help You?</h2>
                    <div class="row g-4">
                        <div class="col-md-4">
                            <div class="service-cta-card">
                                <div class="service-cta-icon">
                                    <i class="fas fa-palette"></i>
                                </div>
                                <h5>Graphic Design</h5>
                                <p>Brand identity, logos, marketing materials, and print design solutions.</p>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="service-cta-card">
                                <div class="service-cta-icon">
                                    <i class="fas fa-code"></i>
                                </div>
                                <h5>Web Development</h5>
                                <p>Modern websites and web applications built with Django and latest technologies.</p>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="service-cta-card">
                                <div class="service-cta-icon">
                                    <i class="fas fa-calculator"></i>
                                </div>
                                <h5>Finance Solutions</h5>
                                <p>Accurate accounting software implementation and financial process optimization.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="section-padding">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="fade-in">
                    <h2 class="section-title vintage-heading">Frequently Asked Questions</h2>
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    What is your typical project timeline?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Project timelines vary depending on scope and complexity. Graphic design projects typically take 1-2 weeks, 
                                    web development projects 2-6 weeks, and finance implementations 1-4 weeks. I'll provide a detailed timeline 
                                    during our initial consultation.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    Do you work with clients internationally?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Yes! I work with clients worldwide. Thanks to modern communication tools and project management systems, 
                                    I can collaborate effectively regardless of location and time zones.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    What information do you need to start a project?
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    I'll need details about your project goals, target audience, preferred style/functionality, timeline, 
                                    and budget. For web projects, information about hosting preferences and any existing systems is helpful. 
                                    Don't worry - I'll guide you through everything during our consultation.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
    .contact-info {
        margin-bottom: 2rem;
    }
    
    .contact-item {
        display: flex;
        align-items: center;
        margin-bottom: 2rem;
        padding: 1rem;
        background: rgba(80, 200, 120, 0.05);
        border-radius: 10px;
        transition: all 0.3s ease;
    }
    
    .contact-item:hover {
        background: rgba(80, 200, 120, 0.1);
        transform: translateX(5px);
    }
    
    .contact-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(45deg, var(--emerald-green), var(--gold));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        color: white;
        font-size: 1.5rem;
    }
    
    .contact-details h5 {
        margin-bottom: 0.5rem;
        color: var(--emerald-green);
    }
    
    .contact-details a {
        color: var(--text-dark);
        text-decoration: none;
        transition: color 0.3s ease;
    }
    
    .contact-details a:hover {
        color: var(--emerald-green);
    }
    
    .contact-form-container {
        background: white;
        padding: 2.5rem;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(80, 200, 120, 0.1);
    }
    
    .contact-form .form-label {
        font-weight: 600;
        color: var(--emerald-green);
        margin-bottom: 0.5rem;
    }
    
    .contact-form .form-control {
        border: 2px solid rgba(80, 200, 120, 0.2);
        border-radius: 8px;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }
    
    .contact-form .form-control:focus {
        border-color: var(--emerald-green);
        box-shadow: 0 0 0 0.2rem rgba(80, 200, 120, 0.25);
    }
    
    .social-links-contact {
        display: flex;
        gap: 1rem;
    }
    
    .service-cta-card {
        text-align: center;
        padding: 2rem 1rem;
        background: white;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        height: 100%;
    }
    
    .service-cta-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(80, 200, 120, 0.15);
    }
    
    .service-cta-icon {
        font-size: 2.5rem;
        color: var(--emerald-green);
        margin-bottom: 1rem;
    }
    
    .service-cta-card h5 {
        color: var(--emerald-green);
        margin-bottom: 1rem;
    }
    
    .accordion-button {
        background: rgba(80, 200, 120, 0.05);
        border: none;
        color: var(--emerald-green);
        font-weight: 600;
    }
    
    .accordion-button:not(.collapsed) {
        background: var(--emerald-green);
        color: white;
    }
    
    .accordion-button:focus {
        box-shadow: 0 0 0 0.25rem rgba(80, 200, 120, 0.25);
    }
    
    @media (max-width: 768px) {
        .contact-form-container {
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .contact-item {
            flex-direction: column;
            text-align: center;
        }
        
        .contact-icon {
            margin-right: 0;
            margin-bottom: 1rem;
        }
        
        .social-links-contact {
            justify-content: center;
        }
    }
</style>
{% endblock %}
