<svg width="200" height="60" viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#50C878;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3a9b5c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFD700;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="30" cy="30" r="25" fill="url(#logoGradient)" opacity="0.1"/>
  
  <!-- Main logo mark -->
  <g filter="url(#glow)">
    <!-- Letter M -->
    <path d="M10 45 L10 15 L18 15 L25 35 L32 15 L40 15 L40 45 L33 45 L33 25 L28 40 L22 40 L17 25 L17 45 Z" 
          fill="url(#logoGradient)" stroke="none"/>
    
    <!-- Letter T -->
    <path d="M45 22 L60 22 L60 15 L75 15 L75 22 L90 22 L90 29 L75 29 L75 45 L60 45 L60 29 L45 29 Z" 
          fill="url(#logoGradient)" stroke="none"/>
  </g>
  
  <!-- Company name -->
  <text x="105" y="25" font-family="serif" font-size="14" font-weight="bold" fill="url(#logoGradient)">
    Muhammad Trinanda
  </text>
  <text x="105" y="40" font-family="sans-serif" font-size="10" fill="#666">
    Creative Professional
  </text>
  
  <!-- Decorative elements -->
  <circle cx="15" cy="15" r="2" fill="#FFD700" opacity="0.6"/>
  <circle cx="185" cy="45" r="1.5" fill="#50C878" opacity="0.6"/>
  <path d="M95 35 L100 30 L105 35" stroke="#FFD700" stroke-width="1" fill="none" opacity="0.7"/>
</svg>
