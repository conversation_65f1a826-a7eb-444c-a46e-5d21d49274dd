# Muhammad Trinanda Portfolio - Deployment Guide

## Project Overview
A professional portfolio website for <PERSON> Trinanda, showcasing expertise in:
- Graphic Design
- Web Development  
- Finance (Accurate Software Specialist)

## Features Implemented
✅ **Core Pages**
- Home page with hero section and services overview
- About page with biography and skills
- Projects page with "Coming Soon" placeholders
- Contact page with working contact form

✅ **Design & UX**
- Elegant vintage theme with emerald green (#50C878) & gold (#FFD700)
- Fully responsive design for all devices
- Smooth scrolling and fade-in animations
- Professional typography (Playfair Display + Lato)

✅ **Technical Features**
- Django 5.2.3 framework
- Bootstrap 5 for responsive layout
- SEO optimized with meta tags and sitemap
- Contact form with email backend
- Admin panel for future project management
- Lazy loading for images
- Scroll-to-top button

✅ **Admin Panel**
- Project categories management
- Individual projects with detailed fields
- Image gallery support
- Client testimonials
- SEO fields for each project

## Local Development Setup

### Prerequisites
- Python 3.8+
- pip package manager

### Installation Steps
1. **Clone/Navigate to project directory**
   ```bash
   cd "d:\.Project Website\6 Website Portfolio Muhammad Trinanda"
   ```

2. **Activate virtual environment**
   ```bash
   portfolio_env\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Run migrations**
   ```bash
   python manage.py migrate
   ```

5. **Create superuser (optional)**
   ```bash
   python manage.py createsuperuser
   ```

6. **Run development server**
   ```bash
   python manage.py runserver
   ```

7. **Access the website**
   - Website: http://127.0.0.1:8000/
   - Admin: http://127.0.0.1:8000/admin/

## Deployment Options

### Option 1: Render.com (Recommended)
1. Create account on Render.com
2. Connect GitHub repository
3. Configure environment variables:
   ```
   DJANGO_SECRET_KEY=your-secret-key
   DEBUG=False
   ALLOWED_HOSTS=your-domain.com
   ```
4. Set build command: `pip install -r requirements.txt`
5. Set start command: `python manage.py migrate && python manage.py collectstatic --noinput && gunicorn portfolio_site.wsgi:application`

### Option 2: Heroku
1. Install Heroku CLI
2. Create Procfile: `web: gunicorn portfolio_site.wsgi`
3. Add to requirements.txt: `gunicorn==20.1.0`
4. Configure environment variables in Heroku dashboard
5. Deploy: `git push heroku main`

### Option 3: VPS/Traditional Hosting
1. Install Python, pip, and web server (nginx/apache)
2. Set up virtual environment
3. Configure static files serving
4. Set up SSL certificate
5. Configure domain DNS

## Environment Variables for Production
```
DEBUG=False
SECRET_KEY=your-very-secret-key-here
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
DATABASE_URL=your-database-url (if using external DB)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
```

## File Structure
```
portfolio_site/
├── core/                   # Main app (home, about, contact)
├── projects/              # Projects app with admin models
├── static/               # CSS, JS, images
├── templates/            # HTML templates
├── portfolio_site/       # Django settings
├── manage.py
├── requirements.txt
└── deployment_guide.md
```

## Contact Form Configuration
The contact form is configured to use Django's console email backend for development. For production:

1. Update `portfolio_site/settings.py`:
   ```python
   EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
   EMAIL_HOST = 'smtp.gmail.com'
   EMAIL_PORT = 587
   EMAIL_USE_TLS = True
   EMAIL_HOST_USER = '<EMAIL>'
   EMAIL_HOST_PASSWORD = 'your-app-password'
   ```

2. For Gmail, create an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"

## Adding Projects (Future)
1. Access admin panel: `/admin/`
2. Add Project Categories (Graphic Design, Web Development, Finance)
3. Create Projects with:
   - Title, description, images
   - Client information
   - Technologies used
   - Live URLs
4. Projects will automatically appear on the portfolio page

## SEO Features
- Meta descriptions and keywords for each page
- Open Graph tags for social sharing
- XML sitemap at `/sitemap.xml`
- Robots.txt at `/robots.txt`
- Structured data ready

## Performance Optimizations
- Lazy loading for images
- Minified CSS and JS (production)
- Optimized database queries
- Static file compression ready
- CDN-ready static files

## Security Features
- CSRF protection on forms
- SQL injection protection (Django ORM)
- XSS protection
- Secure headers ready for production
- Admin panel protection

## Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Maintenance
- Regular Django security updates
- Database backups (if using external DB)
- SSL certificate renewal
- Monitor contact form submissions
- Update portfolio projects regularly

## Support
For technical support or customizations, contact:
- Email: <EMAIL>
- LinkedIn: https://www.linkedin.com/in/mtrinanda/

---
**Built with Django 5.2.3 | Bootstrap 5 | Modern Web Standards**
