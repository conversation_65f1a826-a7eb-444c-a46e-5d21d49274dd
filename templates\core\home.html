{% extends 'base.html' %} {% load static %} {% block title %}{{ page_title }}{%
endblock %} {% block description %}{{ meta_description }}{% endblock %} {% block
content %}
<!-- Hero Section -->
<section class="hero-section" id="home">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-10 text-center">
        <div class="hero-content fade-in">
          <h1 class="hero-title"><PERSON></h1>
          <p class="hero-subtitle">
            Transforming Ideas into Digital Excellence
          </p>
          <p class="hero-description">
            Expert Freelance Graphic Designer • Django Web Developer • Accurate
            Software Specialist
          </p>
          <p class="hero-tagline typing-effect">
            Crafting Beautiful Solutions That Drive Results
          </p>
          <div class="hero-buttons">
            <a
              href="{% url 'projects:portfolio' %}"
              class="btn btn-vintage me-3"
              ><i class="fas fa-eye me-2"></i>Explore My Work</a
            >
            <a
              href="{% url 'core:contact' %}"
              class="btn btn-outline-light btn-lg"
              ><i class="fas fa-rocket me-2"></i>Start Your Project</a
            >
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Decorative Elements -->
  <div class="hero-decoration">
    <div
      class="floating-element"
      style="top: 20%; left: 10%; animation-delay: 0s"
    >
      <i
        class="fas fa-palette"
        style="color: var(--gold); font-size: 2rem; opacity: 0.3"
      ></i>
    </div>
    <div
      class="floating-element"
      style="top: 60%; right: 15%; animation-delay: 2s"
    >
      <i
        class="fas fa-code"
        style="color: var(--emerald-green); font-size: 1.5rem; opacity: 0.3"
      ></i>
    </div>
    <div
      class="floating-element"
      style="top: 40%; left: 80%; animation-delay: 4s"
    >
      <i
        class="fas fa-calculator"
        style="color: var(--gold); font-size: 1.8rem; opacity: 0.3"
      ></i>
    </div>
  </div>
</section>

<!-- Statistics Section -->
<section class="section-padding bg-light" id="stats">
  <div class="container">
    <div class="row">
      <div class="col-12 text-center mb-5">
        <h2 class="section-title vintage-heading fade-in">
          Professional Achievements
        </h2>
        <p class="lead text-muted fade-in">Numbers that speak for themselves</p>
      </div>
    </div>
    <div class="row g-4">
      <div class="col-lg-3 col-md-6">
        <div class="stat-card fade-in text-center">
          <div class="stat-icon">
            <i class="fas fa-project-diagram"></i>
          </div>
          <div class="stat-number" data-target="50">0</div>
          <div class="stat-label">Projects Completed</div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="stat-card fade-in text-center">
          <div class="stat-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="stat-number" data-target="35">0</div>
          <div class="stat-label">Happy Clients</div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="stat-card fade-in text-center">
          <div class="stat-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="stat-number" data-target="2000">0</div>
          <div class="stat-label">Hours of Work</div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="stat-card fade-in text-center">
          <div class="stat-icon">
            <i class="fas fa-award"></i>
          </div>
          <div class="stat-number" data-target="5">0</div>
          <div class="stat-label">Years Experience</div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Services Overview Section -->
<section class="section-padding" id="services">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <h2 class="section-title vintage-heading fade-in">My Expertise</h2>
      </div>
    </div>
    <div class="row g-4">
      <!-- Graphic Design -->
      <div class="col-lg-4 col-md-6">
        <div class="vintage-card fade-in text-center">
          <div class="service-icon mb-4">
            <i
              class="fas fa-palette"
              style="font-size: 3rem; color: var(--emerald-green)"
            ></i>
          </div>
          <h4 class="vintage-heading">Graphic Design Excellence</h4>
          <p class="text-muted">
            Transform your brand identity with visually stunning designs that
            captivate and convert. I specialize in creating memorable logos,
            compelling marketing materials, and cohesive brand experiences that
            make your business stand out in today's competitive market.
          </p>
          <ul class="list-unstyled mt-3">
            <li><i class="fas fa-check text-success me-2"></i>Logo Design</li>
            <li>
              <i class="fas fa-check text-success me-2"></i>Brand Identity
            </li>
            <li>
              <i class="fas fa-check text-success me-2"></i>Marketing Materials
            </li>
            <li><i class="fas fa-check text-success me-2"></i>Print Design</li>
          </ul>
        </div>
      </div>

      <!-- Web Development -->
      <div class="col-lg-4 col-md-6">
        <div class="vintage-card fade-in text-center">
          <div class="service-icon mb-4">
            <i
              class="fas fa-code"
              style="font-size: 3rem; color: var(--gold)"
            ></i>
          </div>
          <h4 class="vintage-heading">Web Development Mastery</h4>
          <p class="text-muted">
            Bring your digital vision to life with powerful, scalable web
            solutions. Using Django and modern technologies, I create responsive
            websites and applications that not only look amazing but perform
            flawlessly across all devices and deliver exceptional user
            experiences.
          </p>
          <ul class="list-unstyled mt-3">
            <li>
              <i class="fas fa-check text-success me-2"></i>Django Development
            </li>
            <li>
              <i class="fas fa-check text-success me-2"></i>Responsive Design
            </li>
            <li>
              <i class="fas fa-check text-success me-2"></i>E-commerce Solutions
            </li>
            <li>
              <i class="fas fa-check text-success me-2"></i>API Integration
            </li>
          </ul>
        </div>
      </div>

      <!-- Finance Specialist -->
      <div class="col-lg-4 col-md-6">
        <div class="vintage-card fade-in text-center">
          <div class="service-icon mb-4">
            <i
              class="fas fa-calculator"
              style="font-size: 3rem; color: var(--emerald-green)"
            ></i>
          </div>
          <h4 class="vintage-heading">Financial Solutions Expert</h4>
          <p class="text-muted">
            Streamline your business finances with expert Accurate software
            implementation and optimization. I help businesses achieve financial
            clarity, improve efficiency, and maintain precise records that
            support informed decision-making and sustainable growth.
          </p>
          <ul class="list-unstyled mt-3">
            <li>
              <i class="fas fa-check text-success me-2"></i>Accurate Software
            </li>
            <li>
              <i class="fas fa-check text-success me-2"></i>Financial Reporting
            </li>
            <li><i class="fas fa-check text-success me-2"></i>Bookkeeping</li>
            <li>
              <i class="fas fa-check text-success me-2"></i>Process Optimization
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Call to Action Section -->
<section
  class="section-padding"
  style="
    background: linear-gradient(
      135deg,
      var(--light-gold) 0%,
      var(--white) 100%
    );
  "
>
  <div class="container">
    <div class="row justify-content-center text-center">
      <div class="col-lg-8">
        <div class="fade-in">
          <h2 class="vintage-heading mb-4">
            Ready to Transform Your Business?
          </h2>
          <p class="lead text-muted mb-4">
            Don't let your competitors get ahead. Whether you need a stunning
            brand identity, a powerful web presence, or streamlined financial
            processes, I'm here to deliver exceptional results that drive real
            business growth. Let's create something extraordinary together.
          </p>
          <div class="cta-buttons">
            <a href="{% url 'core:contact' %}" class="btn btn-vintage me-3"
              >Start a Project</a
            >
            <a href="{% url 'core:about' %}" class="btn btn-outline-dark btn-lg"
              >Learn More About Me</a
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock %} {% block extra_css %}
<style>
  .floating-element {
    position: absolute;
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  .service-icon {
    transition: transform 0.3s ease;
  }

  .vintage-card:hover .service-icon {
    transform: scale(1.1);
  }

  .hero-description {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
    font-weight: 400;
  }

  .hero-buttons .btn {
    margin: 0.5rem;
  }

  .cta-buttons .btn {
    margin: 0.5rem;
  }

  /* Statistics Cards */
  .stat-card {
    background: white;
    padding: 2rem 1rem;
    border-radius: var(--radius-medium);
    box-shadow: 0 10px 30px var(--shadow-light);
    transition: all var(--transition-normal);
    border: 1px solid var(--shadow-light);
    height: 100%;
  }

  .stat-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px var(--shadow-medium);
  }

  .stat-icon {
    font-size: 3rem;
    color: var(--emerald-green);
    margin-bottom: 1rem;
    transition: all var(--transition-normal);
  }

  .stat-card:hover .stat-icon {
    color: var(--gold);
    transform: scale(1.1);
  }

  .stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--emerald-green);
    margin-bottom: 0.5rem;
    font-family: var(--font-heading);
  }

  .stat-label {
    font-size: 1rem;
    color: var(--text-muted);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  @media (max-width: 768px) {
    .hero-buttons .btn,
    .cta-buttons .btn {
      display: block;
      width: 100%;
      margin: 0.5rem 0;
    }

    .stat-card {
      margin-bottom: 2rem;
    }

    .stat-number {
      font-size: 2.5rem;
    }

    .stat-icon {
      font-size: 2.5rem;
    }
  }
</style>
{% endblock %}
