# <PERSON>nanda - Professional Portfolio Website

A sophisticated, responsive portfolio website built with Django, showcasing expertise in Graphic Design, Web Development, and Finance (Accurate Software Specialist).

![Portfolio Preview](static/images/portfolio-preview.png)

## 🌟 Features

### ✨ **Core Functionality**
- **Responsive Design**: Perfect on all devices (mobile-first approach)
- **Dark/Light Mode**: Toggle between themes with smooth transitions
- **Interactive Elements**: Animated counters, skill bars, and smooth scrolling
- **Contact System**: Advanced contact form with spam protection and email notifications
- **Portfolio Showcase**: Dynamic filtering, search, and detailed project pages
- **SEO Optimized**: Meta tags, Open Graph, sitemap, and structured data

### 🎨 **Design & UX**
- **Vintage Elegance**: Emerald green (#50C878) and gold (#FFD700) color scheme
- **Professional Typography**: Playfair Display for headings, Lato for body text
- **Smooth Animations**: Fade-in effects, hover transitions, and loading animations
- **Mobile Optimized**: Touch-friendly interactions and PWA support

### 🔧 **Technical Features**
- **Django 5.2.3**: Modern Python web framework
- **Bootstrap 5**: Responsive CSS framework
- **Progressive Web App**: Installable on mobile devices
- **Analytics Integration**: Google Analytics, Facebook Pixel, LinkedIn Insight
- **Security Hardened**: Rate limiting, CSRF protection, security headers
- **Performance Optimized**: Lazy loading, caching, compression

### 🛡️ **Security & Performance**
- **Rate Limiting**: Protection against spam and abuse
- **Form Validation**: Advanced spam detection and honeypot fields
- **Security Headers**: CSP, HSTS, XSS protection
- **Caching System**: Optimized for fast loading
- **Image Optimization**: Lazy loading and responsive images

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- pip package manager
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/mtrinanda/portfolio-website.git
   cd portfolio-website
   ```

2. **Create virtual environment**
   ```bash
   python -m venv portfolio_env
   
   # Windows
   portfolio_env\Scripts\activate
   
   # macOS/Linux
   source portfolio_env/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Run migrations**
   ```bash
   python manage.py migrate
   ```

5. **Create superuser (optional)**
   ```bash
   python manage.py createsuperuser
   ```

6. **Run development server**
   ```bash
   python manage.py runserver
   ```

7. **Visit the website**
   - Website: http://127.0.0.1:8000/
   - Admin: http://127.0.0.1:8000/admin/

## 📁 Project Structure

```
portfolio_site/
├── core/                   # Main application
│   ├── views.py           # Home, About, Contact views
│   ├── forms.py           # Contact form with validation
│   ├── middleware.py      # Security and performance middleware
│   └── urls.py            # URL routing
├── projects/              # Portfolio management
│   ├── models.py          # Project, Category, Testimonial models
│   ├── admin.py           # Admin interface
│   ├── views.py           # Portfolio views with filtering
│   └── urls.py            # Portfolio URL routing
├── static/                # Static assets
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   ├── images/            # Images and icons
│   └── manifest.json      # PWA manifest
├── templates/             # HTML templates
│   ├── base.html          # Base template
│   ├── core/              # Core app templates
│   ├── projects/          # Portfolio templates
│   └── analytics/         # Analytics templates
├── portfolio_site/        # Django settings
│   ├── settings.py        # Development settings
│   ├── production_settings.py  # Production settings
│   └── urls.py            # Main URL configuration
├── requirements.txt       # Python dependencies
├── deployment_guide.md    # Deployment instructions
└── README.md             # This file
```

## 🎯 Usage

### Adding Projects
1. Access admin panel: `/admin/`
2. Create Project Categories (Graphic Design, Web Development, Finance)
3. Add Projects with:
   - Title, description, and images
   - Client information
   - Technologies used
   - Live project URLs
   - Case study details

### Customizing Content
- **Home Page**: Edit `templates/core/home.html`
- **About Page**: Edit `templates/core/about.html`
- **Styling**: Modify `static/css/style.css`
- **Colors**: Update CSS variables in `:root`

### Contact Form
- Submissions are logged and emailed
- Spam protection with honeypot and validation
- Rate limiting prevents abuse
- Analytics tracking for conversions

## 🌐 Deployment

### Environment Variables
```bash
# Required for production
SECRET_KEY=your-secret-key-here
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# Email configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Analytics (optional)
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
FACEBOOK_PIXEL_ID=your-pixel-id
LINKEDIN_PARTNER_ID=your-partner-id

# Security (for HTTPS)
SECURE_SSL_REDIRECT=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
```

### Deployment Platforms

#### Render.com (Recommended)
1. Connect GitHub repository
2. Set build command: `pip install -r requirements.txt`
3. Set start command: `python manage.py migrate && python manage.py collectstatic --noinput && gunicorn portfolio_site.wsgi:application`
4. Add environment variables

#### Heroku
1. Install Heroku CLI
2. Create `Procfile`: `web: gunicorn portfolio_site.wsgi`
3. Add `gunicorn` to requirements.txt
4. Deploy: `git push heroku main`

#### VPS/Traditional Hosting
1. Set up Python environment
2. Configure web server (nginx/apache)
3. Set up SSL certificate
4. Configure domain DNS

## 🔧 Maintenance

### Regular Tasks
- **Weekly**: Check contact form submissions
- **Monthly**: Update dependencies, review analytics
- **Quarterly**: Security audit, performance review
- **Annually**: Content refresh, design updates

### Monitoring
- **Analytics**: Google Analytics dashboard
- **Performance**: Page speed insights
- **Security**: Regular vulnerability scans
- **Uptime**: Monitor website availability

### Backup Strategy
- **Database**: Weekly automated backups
- **Media Files**: Cloud storage sync
- **Code**: Git repository with tags
- **Configuration**: Environment variables backup

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support or questions:
- **Email**: <EMAIL>
- **LinkedIn**: [Muhammad Trinanda](https://www.linkedin.com/in/mtrinanda/)
- **Issues**: [GitHub Issues](https://github.com/mtrinanda/portfolio-website/issues)

## 🙏 Acknowledgments

- **Django**: Web framework
- **Bootstrap**: CSS framework
- **Font Awesome**: Icons
- **Google Fonts**: Typography
- **Unsplash**: Stock images (if used)

---

**Built with ❤️ by Muhammad Trinanda**

*Professional Freelance Graphic Designer | Web Developer | Finance Specialist*
