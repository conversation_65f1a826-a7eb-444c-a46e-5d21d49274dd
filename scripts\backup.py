#!/usr/bin/env python
"""
Automated backup script for <PERSON>'s Portfolio Website
This script creates backups of the database, media files, and configuration.
"""

import os
import sys
import shutil
import datetime
import subprocess
import zipfile
import json
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent.parent
sys.path.append(str(project_dir))

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'portfolio_site.settings')
import django
django.setup()

from django.conf import settings
from django.core.management import call_command
from django.core.mail import send_mail


class PortfolioBackup:
    def __init__(self):
        self.project_root = project_dir
        self.backup_dir = self.project_root / 'backups'
        self.timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        self.backup_name = f'portfolio_backup_{self.timestamp}'
        self.current_backup_dir = self.backup_dir / self.backup_name
        
        # Create backup directory
        self.backup_dir.mkdir(exist_ok=True)
        self.current_backup_dir.mkdir(exist_ok=True)
        
        self.log_messages = []
    
    def log(self, message):
        """Log a message with timestamp"""
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        self.log_messages.append(log_message)
    
    def backup_database(self):
        """Backup the database"""
        try:
            self.log("Starting database backup...")
            
            db_config = settings.DATABASES['default']
            
            if db_config['ENGINE'] == 'django.db.backends.sqlite3':
                # SQLite backup
                db_path = db_config['NAME']
                backup_path = self.current_backup_dir / 'database.sqlite3'
                shutil.copy2(db_path, backup_path)
                self.log(f"SQLite database backed up to {backup_path}")
                
            elif db_config['ENGINE'] == 'django.db.backends.postgresql':
                # PostgreSQL backup
                backup_path = self.current_backup_dir / 'database.sql'
                cmd = [
                    'pg_dump',
                    '-h', db_config.get('HOST', 'localhost'),
                    '-p', str(db_config.get('PORT', 5432)),
                    '-U', db_config['USER'],
                    '-d', db_config['NAME'],
                    '-f', str(backup_path)
                ]
                
                env = os.environ.copy()
                env['PGPASSWORD'] = db_config['PASSWORD']
                
                subprocess.run(cmd, env=env, check=True)
                self.log(f"PostgreSQL database backed up to {backup_path}")
                
            else:
                self.log(f"Unsupported database engine: {db_config['ENGINE']}")
                return False
                
            return True
            
        except Exception as e:
            self.log(f"Database backup failed: {str(e)}")
            return False
    
    def backup_media_files(self):
        """Backup media files"""
        try:
            self.log("Starting media files backup...")
            
            media_root = Path(settings.MEDIA_ROOT)
            if media_root.exists():
                media_backup_dir = self.current_backup_dir / 'media'
                shutil.copytree(media_root, media_backup_dir)
                self.log(f"Media files backed up to {media_backup_dir}")
            else:
                self.log("No media directory found, skipping media backup")
            
            return True
            
        except Exception as e:
            self.log(f"Media backup failed: {str(e)}")
            return False
    
    def backup_static_files(self):
        """Backup static files"""
        try:
            self.log("Starting static files backup...")
            
            static_root = self.project_root / 'static'
            if static_root.exists():
                static_backup_dir = self.current_backup_dir / 'static'
                shutil.copytree(static_root, static_backup_dir)
                self.log(f"Static files backed up to {static_backup_dir}")
            else:
                self.log("No static directory found, skipping static backup")
            
            return True
            
        except Exception as e:
            self.log(f"Static backup failed: {str(e)}")
            return False
    
    def backup_configuration(self):
        """Backup configuration files"""
        try:
            self.log("Starting configuration backup...")
            
            config_files = [
                'requirements.txt',
                'manage.py',
                'portfolio_site/settings.py',
                'portfolio_site/production_settings.py',
                'portfolio_site/urls.py',
            ]
            
            config_backup_dir = self.current_backup_dir / 'config'
            config_backup_dir.mkdir(exist_ok=True)
            
            for config_file in config_files:
                source_path = self.project_root / config_file
                if source_path.exists():
                    dest_path = config_backup_dir / source_path.name
                    shutil.copy2(source_path, dest_path)
                    self.log(f"Backed up {config_file}")
            
            # Backup environment variables (without sensitive data)
            env_backup = {
                'DJANGO_SETTINGS_MODULE': os.environ.get('DJANGO_SETTINGS_MODULE'),
                'DEBUG': os.environ.get('DEBUG'),
                'ALLOWED_HOSTS': os.environ.get('ALLOWED_HOSTS'),
                # Don't backup sensitive data like SECRET_KEY, passwords, etc.
            }
            
            env_file = config_backup_dir / 'environment.json'
            with open(env_file, 'w') as f:
                json.dump(env_backup, f, indent=2)
            
            self.log("Configuration files backed up")
            return True
            
        except Exception as e:
            self.log(f"Configuration backup failed: {str(e)}")
            return False
    
    def create_backup_info(self):
        """Create backup information file"""
        try:
            backup_info = {
                'backup_name': self.backup_name,
                'timestamp': self.timestamp,
                'date': datetime.datetime.now().isoformat(),
                'django_version': django.get_version(),
                'python_version': sys.version,
                'project_root': str(self.project_root),
                'backup_components': [
                    'database',
                    'media_files',
                    'static_files',
                    'configuration'
                ],
                'log_messages': self.log_messages
            }
            
            info_file = self.current_backup_dir / 'backup_info.json'
            with open(info_file, 'w') as f:
                json.dump(backup_info, f, indent=2)
            
            self.log("Backup information file created")
            return True
            
        except Exception as e:
            self.log(f"Failed to create backup info: {str(e)}")
            return False
    
    def compress_backup(self):
        """Compress the backup directory"""
        try:
            self.log("Compressing backup...")
            
            zip_path = self.backup_dir / f'{self.backup_name}.zip'
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(self.current_backup_dir):
                    for file in files:
                        file_path = Path(root) / file
                        arc_path = file_path.relative_to(self.current_backup_dir)
                        zipf.write(file_path, arc_path)
            
            # Remove uncompressed backup directory
            shutil.rmtree(self.current_backup_dir)
            
            self.log(f"Backup compressed to {zip_path}")
            return zip_path
            
        except Exception as e:
            self.log(f"Compression failed: {str(e)}")
            return None
    
    def cleanup_old_backups(self, keep_count=10):
        """Remove old backup files, keeping only the most recent ones"""
        try:
            self.log(f"Cleaning up old backups, keeping {keep_count} most recent...")
            
            backup_files = list(self.backup_dir.glob('portfolio_backup_*.zip'))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            if len(backup_files) > keep_count:
                for old_backup in backup_files[keep_count:]:
                    old_backup.unlink()
                    self.log(f"Removed old backup: {old_backup.name}")
            
            self.log("Cleanup completed")
            return True
            
        except Exception as e:
            self.log(f"Cleanup failed: {str(e)}")
            return False
    
    def send_notification(self, success, zip_path=None):
        """Send email notification about backup status"""
        try:
            if success:
                subject = f"Portfolio Backup Successful - {self.timestamp}"
                message = f"""
                Backup completed successfully!
                
                Backup Name: {self.backup_name}
                Timestamp: {self.timestamp}
                Backup File: {zip_path.name if zip_path else 'N/A'}
                
                Components backed up:
                - Database
                - Media files
                - Static files
                - Configuration
                
                Log messages:
                {chr(10).join(self.log_messages)}
                """
            else:
                subject = f"Portfolio Backup Failed - {self.timestamp}"
                message = f"""
                Backup failed!
                
                Timestamp: {self.timestamp}
                
                Error log:
                {chr(10).join(self.log_messages)}
                """
            
            # Send email (configure email settings in Django settings)
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                ['<EMAIL>'],
                fail_silently=True,
            )
            
            self.log("Notification email sent")
            
        except Exception as e:
            self.log(f"Failed to send notification: {str(e)}")
    
    def run_backup(self):
        """Run the complete backup process"""
        self.log("Starting portfolio backup process...")
        
        success = True
        
        # Perform backup steps
        if not self.backup_database():
            success = False
        
        if not self.backup_media_files():
            success = False
        
        if not self.backup_static_files():
            success = False
        
        if not self.backup_configuration():
            success = False
        
        if not self.create_backup_info():
            success = False
        
        zip_path = None
        if success:
            zip_path = self.compress_backup()
            if zip_path:
                self.cleanup_old_backups()
            else:
                success = False
        
        # Send notification
        self.send_notification(success, zip_path)
        
        if success:
            self.log("Backup process completed successfully!")
        else:
            self.log("Backup process completed with errors!")
        
        return success


if __name__ == '__main__':
    backup = PortfolioBackup()
    backup.run_backup()
