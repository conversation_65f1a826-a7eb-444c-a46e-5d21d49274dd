<!-- Google Analytics 4 -->
{% if GOOGLE_ANALYTICS_ID %}
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id={{ GOOGLE_ANALYTICS_ID }}"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', '{{ GOOGLE_ANALYTICS_ID }}', {
    // Enhanced measurement
    enhanced_measurement: true,
    // Custom parameters
    custom_map: {
      'custom_parameter_1': 'page_category',
      'custom_parameter_2': 'user_type'
    },
    // Privacy settings
    anonymize_ip: true,
    allow_google_signals: false,
    allow_ad_personalization_signals: false
  });

  // Custom events for portfolio tracking
  function trackPortfolioView(projectTitle, category) {
    gtag('event', 'portfolio_view', {
      'event_category': 'Portfolio',
      'event_label': projectTitle,
      'custom_parameter_1': category,
      'value': 1
    });
  }

  function trackContactFormSubmit(formType) {
    gtag('event', 'contact_form_submit', {
      'event_category': 'Contact',
      'event_label': formType,
      'value': 1
    });
  }

  function trackDownload(fileName, fileType) {
    gtag('event', 'file_download', {
      'event_category': 'Downloads',
      'event_label': fileName,
      'custom_parameter_1': fileType,
      'value': 1
    });
  }

  function trackExternalLink(url, linkText) {
    gtag('event', 'click', {
      'event_category': 'External Links',
      'event_label': linkText,
      'transport_type': 'beacon',
      'custom_parameter_1': url
    });
  }

  function trackServiceInquiry(service) {
    gtag('event', 'service_inquiry', {
      'event_category': 'Services',
      'event_label': service,
      'value': 1
    });
  }

  // Track scroll depth
  let scrollDepthTracked = false;
  function trackScrollDepth() {
    if (scrollDepthTracked) return;
    
    const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
    
    if (scrollPercent >= 75) {
      gtag('event', 'scroll_depth', {
        'event_category': 'Engagement',
        'event_label': '75%',
        'value': scrollPercent
      });
      scrollDepthTracked = true;
    }
  }

  // Track time on page
  let startTime = Date.now();
  window.addEventListener('beforeunload', function() {
    const timeOnPage = Math.round((Date.now() - startTime) / 1000);
    if (timeOnPage > 10) { // Only track if user spent more than 10 seconds
      gtag('event', 'time_on_page', {
        'event_category': 'Engagement',
        'event_label': document.title,
        'value': timeOnPage
      });
    }
  });

  // Add scroll tracking
  window.addEventListener('scroll', trackScrollDepth);
</script>
{% endif %}

<!-- Facebook Pixel (Optional) -->
{% if FACEBOOK_PIXEL_ID %}
<script>
  !function(f,b,e,v,n,t,s)
  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
  n.callMethod.apply(n,arguments):n.queue.push(arguments)};
  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
  n.queue=[];t=b.createElement(e);t.async=!0;
  t.src=v;s=b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t,s)}(window, document,'script',
  'https://connect.facebook.net/en_US/fbevents.js');

  fbq('init', '{{ FACEBOOK_PIXEL_ID }}');
  fbq('track', 'PageView');

  // Custom Facebook events
  function trackFBContactForm() {
    fbq('track', 'Contact');
  }

  function trackFBServiceInquiry(service) {
    fbq('track', 'Lead', {
      content_name: service,
      content_category: 'Service Inquiry'
    });
  }
</script>
<noscript>
  <img height="1" width="1" style="display:none" 
       src="https://www.facebook.com/tr?id={{ FACEBOOK_PIXEL_ID }}&ev=PageView&noscript=1"/>
</noscript>
{% endif %}

<!-- LinkedIn Insight Tag (Optional) -->
{% if LINKEDIN_PARTNER_ID %}
<script type="text/javascript">
_linkedin_partner_id = "{{ LINKEDIN_PARTNER_ID }}";
window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || [];
window._linkedin_data_partner_ids.push(_linkedin_partner_id);
</script><script type="text/javascript">
(function(l) {
if (!l){window.lintrk = function(a,b){window.lintrk.q.push([a,b])};
window.lintrk.q=[]}
var s = document.getElementsByTagName("script")[0];
var b = document.createElement("script");
b.type = "text/javascript";b.async = true;
b.src = "https://snap.licdn.com/li.js";
s.parentNode.insertBefore(b, s);})(window.lintrk);
</script>
<noscript>
<img height="1" width="1" style="display:none;" alt="" src="https://px.ads.linkedin.com/collect/?pid={{ LINKEDIN_PARTNER_ID }}&fmt=gif" />
</noscript>
{% endif %}
