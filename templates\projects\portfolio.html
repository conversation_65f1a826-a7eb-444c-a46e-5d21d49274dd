{% extends 'base.html' %} {% load static %} {% block title %}{{ page_title }}{%
endblock %} {% block description %}{{ meta_description }}{% endblock %} {% block
content %}
<!-- Portfolio Hero Section -->
<section
  class="section-padding"
  style="
    background: linear-gradient(
      135deg,
      var(--dark-bg) 0%,
      var(--dark-emerald) 100%
    );
    color: white;
    margin-top: 80px;
  "
>
  <div class="container">
    <div class="row justify-content-center text-center">
      <div class="col-lg-8">
        <div class="fade-in">
          <h1 class="vintage-heading" style="color: white; font-size: 3rem">
            Selected Works
          </h1>
          <p class="lead" style="color: var(--light-gold)">
            A showcase of my creative journey across graphic design, web
            development, and finance solutions.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Coming Soon Section -->
<section class="section-padding">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-10">
        <!-- Search and Filter Bar -->
        <div class="portfolio-controls fade-in mb-5">
          <div class="row align-items-center">
            <div class="col-md-6">
              <form method="GET" class="search-form">
                <div class="input-group">
                  <input
                    type="text"
                    class="form-control"
                    name="search"
                    placeholder="Search projects..."
                    value="{{ search_query }}"
                  />
                  <button class="btn btn-vintage" type="submit">
                    <i class="fas fa-search"></i>
                  </button>
                </div>
              </form>
            </div>
            <div class="col-md-6">
              <div class="filter-buttons text-md-end mt-3 mt-md-0">
                <a
                  href="{% url 'projects:portfolio' %}"
                  class="filter-btn {% if not current_category %}active{% endif %}"
                  >All</a
                >
                {% for category in categories %}
                <a
                  href="{% url 'projects:portfolio' %}?category={{ category.slug }}"
                  class="filter-btn {% if current_category == category.slug %}active{% endif %}"
                >
                  {{ category.name }}
                </a>
                {% endfor %}
              </div>
            </div>
          </div>
        </div>

        {% if projects %}
        <!-- Results Info -->
        <div class="results-info fade-in mb-4">
          <p class="text-muted">
            Showing {{ projects|length }} of {{ total_projects }} project{{
            total_projects|pluralize }} {% if current_category %}in {{
            current_category }}{% endif %} {% if search_query %}for "{{
            search_query }}"{% endif %}
          </p>
        </div>

        <!-- Projects Grid -->
        <div class="projects-grid fade-in">
          <div class="row g-4">
            {% for project in projects %}
            <div class="col-lg-4 col-md-6">
              <div class="project-card">
                <div class="project-image">
                  {% if project.featured_image %}
                  <img
                    src="{{ project.featured_image.url }}"
                    alt="{{ project.title }}"
                    class="img-fluid"
                  />
                  {% else %}
                  <div class="project-placeholder">
                    <i class="{{ project.category.icon }}"></i>
                    <span>{{ project.category.name }}</span>
                  </div>
                  {% endif %}
                  <div class="project-overlay">
                    <div class="project-actions">
                      <a
                        href="{{ project.get_absolute_url }}"
                        class="btn btn-light btn-sm"
                      >
                        <i class="fas fa-eye"></i>
                      </a>
                      {% if project.project_url %}
                      <a
                        href="{{ project.project_url }}"
                        target="_blank"
                        class="btn btn-light btn-sm"
                      >
                        <i class="fas fa-external-link-alt"></i>
                      </a>
                      {% endif %}
                    </div>
                  </div>
                </div>
                <div class="project-content">
                  <div class="project-category">
                    <span
                      class="category-badge"
                      style="background-color: {{ project.category.color }};"
                    >
                      {{ project.category.name }}
                    </span>
                  </div>
                  <h4>
                    <a href="{{ project.get_absolute_url }}"
                      >{{ project.title }}</a
                    >
                  </h4>
                  <p class="project-description">
                    {{ project.short_description }}
                  </p>
                  <div class="project-meta">
                    <span class="project-date">
                      <i class="fas fa-calendar me-1"></i>
                      {{ project.project_date|date:"M Y" }}
                    </span>
                    {% if project.client %}
                    <span class="project-client">
                      <i class="fas fa-user me-1"></i>
                      {{ project.client }}
                    </span>
                    {% endif %}
                  </div>
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
        </div>
        {% else %}
        <!-- No Projects / Coming Soon -->
        <div class="fade-in text-center mb-5">
          <h2 class="section-title vintage-heading">Portfolio Coming Soon</h2>
          <p class="lead text-muted">
            I'm currently curating my best work to showcase here. Check back
            soon to see amazing projects spanning graphic design, web
            development, and finance solutions.
          </p>
        </div>

        <!-- Project Categories Preview -->
        <div class="row g-4">
          {% endif %}
          <!-- Graphic Design Projects -->
          <div class="col-lg-4 col-md-6">
            <div class="project-category-card fade-in">
              <div class="category-icon">
                <i class="fas fa-palette"></i>
              </div>
              <h4>Graphic Design</h4>
              <p class="text-muted">
                Brand identities, logos, marketing materials, and print designs
                that capture the essence of businesses and communicate their
                message effectively.
              </p>
              <div class="coming-soon-badge">
                <span>Coming Soon</span>
              </div>
              <div class="project-count">
                <span>5+ Projects</span>
              </div>
            </div>
          </div>

          <!-- Web Development Projects -->
          <div class="col-lg-4 col-md-6">
            <div class="project-category-card fade-in">
              <div class="category-icon">
                <i class="fas fa-code"></i>
              </div>
              <h4>Web Development</h4>
              <p class="text-muted">
                Modern, responsive websites and web applications built with
                Django, featuring elegant design and powerful functionality.
              </p>
              <div class="coming-soon-badge">
                <span>Coming Soon</span>
              </div>
              <div class="project-count">
                <span>8+ Projects</span>
              </div>
            </div>
          </div>

          <!-- Finance Solutions -->
          <div class="col-lg-4 col-md-6">
            <div class="project-category-card fade-in">
              <div class="category-icon">
                <i class="fas fa-calculator"></i>
              </div>
              <h4>Finance Solutions</h4>
              <p class="text-muted">
                Accurate accounting software implementations, financial
                reporting systems, and process optimization solutions for
                businesses.
              </p>
              <div class="coming-soon-badge">
                <span>Coming Soon</span>
              </div>
              <div class="project-count">
                <span>10+ Projects</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- What to Expect Section -->
<section class="section-padding" style="background-color: var(--light-bg)">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="fade-in">
          <h2 class="section-title vintage-heading">What to Expect</h2>
          <div class="row g-4">
            <div class="col-md-6">
              <div class="expectation-item">
                <div class="expectation-icon">
                  <i
                    class="fas fa-images"
                    style="color: var(--emerald-green)"
                  ></i>
                </div>
                <h5>Visual Showcases</h5>
                <p>
                  High-quality images and interactive previews of completed
                  projects, demonstrating the range and quality of my work.
                </p>
              </div>
            </div>

            <div class="col-md-6">
              <div class="expectation-item">
                <div class="expectation-icon">
                  <i class="fas fa-info-circle" style="color: var(--gold)"></i>
                </div>
                <h5>Project Details</h5>
                <p>
                  Comprehensive case studies including project objectives,
                  challenges faced, solutions implemented, and results achieved.
                </p>
              </div>
            </div>

            <div class="col-md-6">
              <div class="expectation-item">
                <div class="expectation-icon">
                  <i
                    class="fas fa-tools"
                    style="color: var(--emerald-green)"
                  ></i>
                </div>
                <h5>Technologies Used</h5>
                <p>
                  Detailed breakdown of tools, technologies, and methodologies
                  employed in each project to achieve optimal results.
                </p>
              </div>
            </div>

            <div class="col-md-6">
              <div class="expectation-item">
                <div class="expectation-icon">
                  <i class="fas fa-chart-line" style="color: var(--gold)"></i>
                </div>
                <h5>Impact & Results</h5>
                <p>
                  Measurable outcomes and client testimonials showcasing the
                  real-world impact of each project on business success.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Newsletter Signup Section -->
<section
  class="section-padding"
  style="
    background: linear-gradient(
      135deg,
      var(--emerald-green) 0%,
      var(--gold) 100%
    );
    color: white;
  "
>
  <div class="container">
    <div class="row justify-content-center text-center">
      <div class="col-lg-8">
        <div class="fade-in">
          <h2 class="mb-4" style="color: white">Stay Updated</h2>
          <p class="lead mb-4">
            Be the first to know when new projects are added to my portfolio.
            Get notified about my latest work and insights.
          </p>
          <div class="newsletter-form">
            <p class="mb-4">
              <i class="fas fa-envelope me-2"></i>
              For updates, feel free to reach out at
              <a
                href="mailto:<EMAIL>"
                style="color: white; text-decoration: underline"
              >
                <EMAIL>
              </a>
            </p>
            <a href="{% url 'core:contact' %}" class="btn btn-light btn-lg"
              >Get In Touch</a
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Call to Action -->
<section class="section-padding">
  <div class="container">
    <div class="row justify-content-center text-center">
      <div class="col-lg-8">
        <div class="fade-in">
          <h2 class="vintage-heading mb-4">Ready to Start Your Project?</h2>
          <p class="lead text-muted mb-4">
            While you wait for the portfolio showcase, why not discuss your next
            project? I'm always excited to take on new challenges and create
            something amazing.
          </p>
          <div class="cta-buttons">
            <a href="{% url 'core:contact' %}" class="btn btn-vintage me-3"
              >Start a Project</a
            >
            <a href="{% url 'core:about' %}" class="btn btn-outline-dark btn-lg"
              >Learn More About Me</a
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock %} {% block extra_css %}
<style>
  /* Portfolio Controls */
  .portfolio-controls {
    background: white;
    padding: 2rem;
    border-radius: var(--radius-medium);
    box-shadow: 0 10px 30px var(--shadow-light);
    border: 1px solid var(--shadow-light);
  }

  .search-form .form-control {
    border-radius: var(--radius-round) 0 0 var(--radius-round);
    border-right: none;
    padding: 0.75rem 1.5rem;
  }

  .search-form .btn {
    border-radius: 0 var(--radius-round) var(--radius-round) 0;
    padding: 0.75rem 1.5rem;
  }

  .filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: flex-end;
  }

  .filter-btn {
    padding: 0.5rem 1rem;
    border-radius: var(--radius-round);
    text-decoration: none;
    color: var(--text-dark);
    background: var(--light-bg);
    border: 1px solid var(--shadow-light);
    transition: all var(--transition-normal);
    font-weight: 500;
  }

  .filter-btn:hover,
  .filter-btn.active {
    background: var(--gradient-primary);
    color: white;
    border-color: var(--emerald-green);
    transform: translateY(-2px);
  }

  /* Project Cards */
  .project-card {
    background: white;
    border-radius: var(--radius-medium);
    overflow: hidden;
    box-shadow: 0 10px 30px var(--shadow-light);
    transition: all var(--transition-normal);
    border: 1px solid var(--shadow-light);
    height: 100%;
  }

  .project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px var(--shadow-medium);
  }

  .project-image {
    position: relative;
    height: 250px;
    overflow: hidden;
  }

  .project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
  }

  .project-card:hover .project-image img {
    transform: scale(1.1);
  }

  .project-placeholder {
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
  }

  .project-placeholder span {
    font-size: 1rem;
    margin-top: 1rem;
    font-weight: 500;
  }

  .project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
  }

  .project-card:hover .project-overlay {
    opacity: 1;
  }

  .project-actions {
    display: flex;
    gap: 0.5rem;
  }

  .project-content {
    padding: 1.5rem;
  }

  .category-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-round);
    color: white;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  .project-content h4 {
    margin-bottom: 1rem;
  }

  .project-content h4 a {
    color: var(--text-dark);
    text-decoration: none;
    transition: color var(--transition-normal);
  }

  .project-content h4 a:hover {
    color: var(--emerald-green);
  }

  .project-description {
    color: var(--text-muted);
    margin-bottom: 1rem;
    line-height: 1.6;
  }

  .project-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: 0.9rem;
    color: var(--text-light);
  }

  .results-info {
    text-align: center;
  }

  .project-category-card {
    background: white;
    border-radius: 15px;
    padding: 2.5rem 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(80, 200, 120, 0.1);
    position: relative;
    overflow: hidden;
    height: 100%;
    min-height: 350px;
  }

  .project-category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(80, 200, 120, 0.2);
  }

  .category-icon {
    font-size: 4rem;
    color: var(--emerald-green);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
  }

  .project-category-card:hover .category-icon {
    transform: scale(1.1);
    color: var(--gold);
  }

  .coming-soon-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, var(--emerald-green), var(--gold));
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
  }

  .project-count {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(80, 200, 120, 0.1);
    color: var(--emerald-green);
    padding: 0.5rem 1rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
  }

  .expectation-item {
    text-align: center;
    padding: 1.5rem;
  }

  .expectation-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  .expectation-item h5 {
    color: var(--emerald-green);
    margin-bottom: 1rem;
  }

  .newsletter-form {
    max-width: 500px;
    margin: 0 auto;
  }

  .cta-buttons .btn {
    margin: 0.5rem;
  }

  @media (max-width: 768px) {
    .project-category-card {
      min-height: 300px;
      margin-bottom: 2rem;
    }

    .cta-buttons .btn {
      display: block;
      width: 100%;
      margin: 0.5rem 0;
    }

    .category-icon {
      font-size: 3rem;
    }
  }
</style>
{% endblock %}
