#!/usr/bin/env python
"""
Dependency update script for <PERSON>'s Portfolio Website
This script safely updates Python dependencies with testing and rollback capabilities.
"""

import os
import sys
import subprocess
import json
import datetime
import shutil
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent.parent
sys.path.append(str(project_dir))


class DependencyUpdater:
    def __init__(self):
        self.project_root = project_dir
        self.requirements_file = self.project_root / 'requirements.txt'
        self.backup_dir = self.project_root / 'dependency_backups'
        self.timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Create backup directory
        self.backup_dir.mkdir(exist_ok=True)
        
        self.log_messages = []
    
    def log(self, message):
        """Log a message with timestamp"""
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        self.log_messages.append(log_message)
    
    def backup_current_environment(self):
        """Create backup of current environment"""
        try:
            self.log("Creating backup of current environment...")
            
            # Backup requirements.txt
            backup_requirements = self.backup_dir / f'requirements_{self.timestamp}.txt'
            shutil.copy2(self.requirements_file, backup_requirements)
            
            # Create pip freeze backup
            result = subprocess.run(
                [sys.executable, '-m', 'pip', 'freeze'],
                capture_output=True,
                text=True,
                check=True
            )
            
            freeze_backup = self.backup_dir / f'pip_freeze_{self.timestamp}.txt'
            with open(freeze_backup, 'w') as f:
                f.write(result.stdout)
            
            self.log(f"Environment backed up to {backup_requirements} and {freeze_backup}")
            return True
            
        except Exception as e:
            self.log(f"Failed to backup environment: {str(e)}")
            return False
    
    def check_outdated_packages(self):
        """Check for outdated packages"""
        try:
            self.log("Checking for outdated packages...")
            
            result = subprocess.run(
                [sys.executable, '-m', 'pip', 'list', '--outdated', '--format=json'],
                capture_output=True,
                text=True,
                check=True
            )
            
            outdated_packages = json.loads(result.stdout)
            
            if not outdated_packages:
                self.log("All packages are up to date!")
                return []
            
            self.log(f"Found {len(outdated_packages)} outdated packages:")
            for package in outdated_packages:
                self.log(f"  {package['name']}: {package['version']} -> {package['latest_version']}")
            
            return outdated_packages
            
        except Exception as e:
            self.log(f"Failed to check outdated packages: {str(e)}")
            return None
    
    def update_package(self, package_name, version=None):
        """Update a specific package"""
        try:
            if version:
                package_spec = f"{package_name}=={version}"
            else:
                package_spec = package_name
            
            self.log(f"Updating {package_name}...")
            
            result = subprocess.run(
                [sys.executable, '-m', 'pip', 'install', '--upgrade', package_spec],
                capture_output=True,
                text=True,
                check=True
            )
            
            self.log(f"Successfully updated {package_name}")
            return True
            
        except subprocess.CalledProcessError as e:
            self.log(f"Failed to update {package_name}: {e.stderr}")
            return False
    
    def run_tests(self):
        """Run Django tests to verify updates"""
        try:
            self.log("Running Django tests...")
            
            # Set Django settings
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'portfolio_site.settings')
            
            result = subprocess.run(
                [sys.executable, 'manage.py', 'test'],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                check=True
            )
            
            self.log("All tests passed!")
            return True
            
        except subprocess.CalledProcessError as e:
            self.log(f"Tests failed: {e.stderr}")
            return False
    
    def check_django_compatibility(self):
        """Check Django system for any issues"""
        try:
            self.log("Checking Django system...")
            
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'portfolio_site.settings')
            
            result = subprocess.run(
                [sys.executable, 'manage.py', 'check'],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                check=True
            )
            
            self.log("Django system check passed!")
            return True
            
        except subprocess.CalledProcessError as e:
            self.log(f"Django system check failed: {e.stderr}")
            return False
    
    def update_requirements_file(self):
        """Update requirements.txt with current package versions"""
        try:
            self.log("Updating requirements.txt...")
            
            result = subprocess.run(
                [sys.executable, '-m', 'pip', 'freeze'],
                capture_output=True,
                text=True,
                check=True
            )
            
            # Filter out packages that shouldn't be in requirements.txt
            excluded_packages = ['pkg-resources', 'setuptools', 'pip', 'wheel']
            lines = []
            
            for line in result.stdout.strip().split('\n'):
                if line and not any(line.startswith(pkg) for pkg in excluded_packages):
                    lines.append(line)
            
            with open(self.requirements_file, 'w') as f:
                f.write('\n'.join(sorted(lines)) + '\n')
            
            self.log("requirements.txt updated successfully")
            return True
            
        except Exception as e:
            self.log(f"Failed to update requirements.txt: {str(e)}")
            return False
    
    def rollback_environment(self, backup_timestamp):
        """Rollback to a previous environment backup"""
        try:
            self.log(f"Rolling back to backup from {backup_timestamp}...")
            
            backup_file = self.backup_dir / f'pip_freeze_{backup_timestamp}.txt'
            
            if not backup_file.exists():
                self.log(f"Backup file not found: {backup_file}")
                return False
            
            # Uninstall all packages first
            result = subprocess.run(
                [sys.executable, '-m', 'pip', 'freeze'],
                capture_output=True,
                text=True,
                check=True
            )
            
            current_packages = [
                line.split('==')[0] for line in result.stdout.strip().split('\n')
                if line and '==' in line
            ]
            
            # Don't uninstall essential packages
            essential_packages = ['pip', 'setuptools', 'wheel']
            packages_to_uninstall = [
                pkg for pkg in current_packages 
                if pkg not in essential_packages
            ]
            
            if packages_to_uninstall:
                subprocess.run(
                    [sys.executable, '-m', 'pip', 'uninstall', '-y'] + packages_to_uninstall,
                    check=True
                )
            
            # Install from backup
            subprocess.run(
                [sys.executable, '-m', 'pip', 'install', '-r', str(backup_file)],
                check=True
            )
            
            self.log("Rollback completed successfully")
            return True
            
        except Exception as e:
            self.log(f"Rollback failed: {str(e)}")
            return False
    
    def safe_update_all(self):
        """Safely update all packages with testing and rollback"""
        self.log("Starting safe dependency update process...")
        
        # Step 1: Backup current environment
        if not self.backup_current_environment():
            self.log("Failed to backup environment, aborting update")
            return False
        
        # Step 2: Check for outdated packages
        outdated_packages = self.check_outdated_packages()
        if outdated_packages is None:
            self.log("Failed to check outdated packages, aborting update")
            return False
        
        if not outdated_packages:
            self.log("No updates needed")
            return True
        
        # Step 3: Update packages one by one
        failed_updates = []
        for package in outdated_packages:
            # Skip Django major version updates (handle manually)
            if package['name'] == 'Django':
                current_major = int(package['version'].split('.')[0])
                latest_major = int(package['latest_version'].split('.')[0])
                
                if latest_major > current_major:
                    self.log(f"Skipping Django major version update: {package['version']} -> {package['latest_version']}")
                    continue
            
            if not self.update_package(package['name']):
                failed_updates.append(package['name'])
        
        # Step 4: Run tests
        if not self.run_tests():
            self.log("Tests failed after updates, rolling back...")
            if self.rollback_environment(self.timestamp):
                self.log("Rollback successful")
            else:
                self.log("Rollback failed! Manual intervention required")
            return False
        
        # Step 5: Check Django compatibility
        if not self.check_django_compatibility():
            self.log("Django compatibility check failed, rolling back...")
            if self.rollback_environment(self.timestamp):
                self.log("Rollback successful")
            else:
                self.log("Rollback failed! Manual intervention required")
            return False
        
        # Step 6: Update requirements.txt
        if not self.update_requirements_file():
            self.log("Failed to update requirements.txt")
            return False
        
        # Summary
        if failed_updates:
            self.log(f"Update completed with some failures: {', '.join(failed_updates)}")
        else:
            self.log("All packages updated successfully!")
        
        self.log("Safe update process completed")
        return True
    
    def update_specific_packages(self, packages):
        """Update specific packages only"""
        self.log(f"Updating specific packages: {', '.join(packages)}")
        
        # Backup first
        if not self.backup_current_environment():
            return False
        
        failed_updates = []
        for package in packages:
            if not self.update_package(package):
                failed_updates.append(package)
        
        # Test after updates
        if not self.run_tests() or not self.check_django_compatibility():
            self.log("Tests failed, rolling back...")
            self.rollback_environment(self.timestamp)
            return False
        
        # Update requirements.txt
        self.update_requirements_file()
        
        if failed_updates:
            self.log(f"Some packages failed to update: {', '.join(failed_updates)}")
            return False
        
        self.log("Specific package updates completed successfully")
        return True


def main():
    updater = DependencyUpdater()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == '--check':
            # Just check for outdated packages
            updater.check_outdated_packages()
        elif sys.argv[1] == '--packages':
            # Update specific packages
            packages = sys.argv[2:]
            updater.update_specific_packages(packages)
        elif sys.argv[1] == '--rollback':
            # Rollback to specific timestamp
            if len(sys.argv) > 2:
                updater.rollback_environment(sys.argv[2])
            else:
                print("Please provide backup timestamp for rollback")
        else:
            print("Unknown option. Use --check, --packages <pkg1> <pkg2>, or --rollback <timestamp>")
    else:
        # Full safe update
        updater.safe_update_all()


if __name__ == '__main__':
    main()
