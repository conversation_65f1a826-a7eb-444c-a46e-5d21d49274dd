from django.contrib import admin
from django.utils.html import format_html
from .models import ProjectCategory, Project, ProjectImage, Testimonial


class ProjectImageInline(admin.TabularInline):
    model = ProjectImage
    extra = 1
    fields = ('image', 'caption', 'order')


@admin.register(ProjectCategory)
class ProjectCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'icon', 'color_display', 'order', 'is_active', 'project_count')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'description')
    prepopulated_fields = {'slug': ('name',)}
    list_editable = ('order', 'is_active')
    ordering = ('order', 'name')

    def color_display(self, obj):
        return format_html(
            '<span style="background-color: {}; padding: 2px 8px; border-radius: 3px; color: white;">{}</span>',
            obj.color,
            obj.color
        )
    color_display.short_description = 'Color'

    def project_count(self, obj):
        return obj.projects.count()
    project_count.short_description = 'Projects'


@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ('title', 'category', 'client', 'project_date', 'status', 'is_featured', 'order')
    list_filter = ('status', 'category', 'is_featured', 'project_date', 'created_at')
    search_fields = ('title', 'description', 'client', 'technologies', 'tools')
    prepopulated_fields = {'slug': ('title',)}
    list_editable = ('status', 'is_featured', 'order')
    date_hierarchy = 'project_date'
    ordering = ('-is_featured', 'order', '-project_date')

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'slug', 'category', 'status', 'is_featured', 'order')
        }),
        ('Content', {
            'fields': ('short_description', 'description')
        }),
        ('Project Details', {
            'fields': ('client', 'project_date', 'project_url', 'github_url')
        }),
        ('Media', {
            'fields': ('featured_image', 'gallery_images'),
            'classes': ('collapse',)
        }),
        ('Technical Details', {
            'fields': ('technologies', 'tools'),
            'classes': ('collapse',)
        }),
        ('SEO', {
            'fields': ('meta_description', 'meta_keywords'),
            'classes': ('collapse',)
        }),
    )

    inlines = [ProjectImageInline]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('category')


@admin.register(ProjectImage)
class ProjectImageAdmin(admin.ModelAdmin):
    list_display = ('project', 'caption', 'order', 'created_at')
    list_filter = ('project__category', 'created_at')
    search_fields = ('project__title', 'caption')
    list_editable = ('order',)
    ordering = ('project', 'order')


@admin.register(Testimonial)
class TestimonialAdmin(admin.ModelAdmin):
    list_display = ('name', 'company', 'project', 'rating', 'is_featured', 'created_at')
    list_filter = ('rating', 'is_featured', 'created_at', 'project__category')
    search_fields = ('name', 'company', 'content')
    list_editable = ('is_featured',)
    ordering = ('-is_featured', '-created_at')

    fieldsets = (
        ('Client Information', {
            'fields': ('name', 'company', 'position')
        }),
        ('Testimonial', {
            'fields': ('content', 'rating', 'project', 'is_featured')
        }),
    )


# Customize admin site
admin.site.site_header = 'Muhammad Trinanda Portfolio Admin'
admin.site.site_title = 'Portfolio Admin'
admin.site.index_title = 'Welcome to Portfolio Administration'
